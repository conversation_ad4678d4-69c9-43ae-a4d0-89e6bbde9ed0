#!/usr/bin/env python3
"""
AllReduce结果验证脚本
验证test_allreduce函数的输出是否正确
"""

import torch
import numpy as np
import os
import json
from pathlib import Path
import argparse
from typing import List, Dict, Tuple

# 核心配置常量
NUM_CORES = 16
NUM_GROUPS = 4
CORES_PER_GROUP = 4

# 核心拓扑结构（与C代码一致）
CORE_CORD = [
    0x0000, 0x0001, 0x0002, 0x0003, 0x0100, 0x0101, 0x0102, 0x0103,
    0x0200, 0x0201, 0x0202, 0x0203, 0x0300, 0x0301, 0x0302, 0x0303
]

LOOP = [0, 1, 2, 3, 4, 5, 6, 7, 15, 14, 13, 12, 11, 10, 9, 8]

def get_core_rank():
    """计算核心排名"""
    core_rank = [0] * NUM_CORES
    for i in range(NUM_CORES):
        node_index = LOOP[i]
        core_rank[node_index] = i
    return core_rank

def load_tensor_configs():
    """加载tensor配置信息"""
    input_config_path = "input_tensor.json"
    output_config_path = "output_tensor.json"
    
    configs = {}
    
    if os.path.exists(input_config_path):
        with open(input_config_path, 'r') as f:
            configs['input'] = json.load(f)
    
    if os.path.exists(output_config_path):
        with open(output_config_path, 'r') as f:
            configs['output'] = json.load(f)
    
    return configs

def load_pytorch_tensor(file_path):
    """加载PyTorch tensor文件"""
    try:
        tensor = torch.load(file_path, map_location='cpu')
        return tensor
    except Exception as e:
        print(f"Error loading {file_path}: {e}")
        return None

def compute_expected_allreduce(input_tensors: Dict[str, torch.Tensor], debug=False) -> Dict[str, torch.Tensor]:
    """
    计算期望的AllReduce结果
    
    AllReduce操作：
    1. ReduceScatter: 每个核心负责部分数据的累加
    2. AllGather: 广播累加结果到所有核心
    
    最终结果：每个核心都有所有核心数据的总和
    """
    if debug:
        print("\n🔍 调试：计算期望的AllReduce结果...")
    
    # 收集所有input1和input2
    all_input1 = []
    all_input2 = []
    
    for key, tensor in input_tensors.items():
        if "input1" in key:
            all_input1.append(tensor)
        elif "input2" in key:
            all_input2.append(tensor)
    
    if debug:
        print(f"  找到 {len(all_input1)} 个input1张量")
        print(f"  找到 {len(all_input2)} 个input2张量")
    
    # 计算AllReduce结果
    # 对于input1：所有核心的input1累加
    if all_input1:
        # 累加所有核心的input1
        result_input1 = torch.zeros_like(all_input1[0])
        for tensor in all_input1:
            result_input1 += tensor
        
        if debug:
            # 打印第一个元素的值作为调试
            first_val = all_input1[0].flatten()[0].item()
            result_val = result_input1.flatten()[0].item()
            print(f"  Input1: 单个核心第一个元素 = {first_val:.4f}")
            print(f"  Input1: AllReduce后第一个元素 = {result_val:.4f}")
            print(f"  Input1: 期望值 = {first_val * len(all_input1):.4f}")
    else:
        result_input1 = None
    
    # 对于input2：所有核心的input2累加
    if all_input2:
        # 累加所有核心的input2
        result_input2 = torch.zeros_like(all_input2[0])
        for tensor in all_input2:
            result_input2 += tensor
        
        if debug:
            # 打印第一个元素的值作为调试
            first_val = all_input2[0].flatten()[0].item()
            result_val = result_input2.flatten()[0].item()
            print(f"  Input2: 单个核心第一个元素 = {first_val:.4f}")
            print(f"  Input2: AllReduce后第一个元素 = {result_val:.4f}")
            print(f"  Input2: 期望值 = {first_val * len(all_input2):.4f}")
    else:
        result_input2 = None
    
    # 返回期望的输出
    expected_outputs = {}
    
    # 每个核心的output1应该是所有input1的累加
    # 每个核心的output2应该是所有input2的累加
    for group_id in range(NUM_GROUPS):
        for core_id in range(CORES_PER_GROUP):
            if result_input1 is not None:
                expected_outputs[f"output1_{group_id}_{core_id}"] = result_input1.clone()
            if result_input2 is not None:
                expected_outputs[f"output2_{group_id}_{core_id}"] = result_input2.clone()
    
    return expected_outputs

def verify_allreduce(debug=True):
    """
    验证AllReduce结果
    
    Args:
        debug: 是否打印调试信息
    """
    print("🔍 开始验证AllReduce结果...")
    
    # 加载配置
    configs = load_tensor_configs()
    if not configs:
        print("❌ 无法加载配置文件")
        return False
    
    # 加载输入数据
    print("\n📥 加载输入数据...")
    input_tensors = {}
    in_path = Path("in_data")
    
    if not in_path.exists():
        print(f"❌ 输入目录不存在: {in_path}")
        return False
    
    # 加载所有输入张量
    input_count = 0
    for pt_file in sorted(in_path.glob("*.pt")):
        tensor = load_pytorch_tensor(pt_file)
        if tensor is not None:
            input_tensors[pt_file.stem] = tensor
            if input_count < 3:  # 显示前几个
                print(f"  ✓ 加载 {pt_file.stem}: shape={tensor.shape}, dtype={tensor.dtype}")
            input_count += 1
    
    if input_count > 3:
        print(f"  ... 共加载 {input_count} 个输入张量")
    
    if not input_tensors:
        print("❌ 没有找到输入数据")
        return False
    
    # 打印输入数据的样本值
    if debug:
        print("\n🔍 调试：输入数据样本")
        sample_keys = ["input1_0_0", "input2_0_0"]
        for key in sample_keys:
            if key in input_tensors:
                tensor = input_tensors[key]
                # 打印前5个元素
                values = tensor.flatten()[:5].tolist()
                print(f"  {key} 前5个元素: {[f'{v:.4f}' for v in values]}")
                print(f"  {key} 均值: {tensor.mean().item():.4f}")
    
    # 加载输出数据
    print("\n📥 加载输出数据...")
    output_tensors = {}
    out_path = Path("out_data")
    
    if not out_path.exists():
        print(f"❌ 输出目录不存在: {out_path}")
        print("   提示: 输出数据应该由simulator执行后生成")
        return False
    
    output_count = 0
    for pt_file in sorted(out_path.glob("*.pt")):
        tensor = load_pytorch_tensor(pt_file)
        if tensor is not None:
            output_tensors[pt_file.stem] = tensor
            if output_count < 3:  # 显示前几个
                print(f"  ✓ 加载 {pt_file.stem}: shape={tensor.shape}, dtype={tensor.dtype}")
            output_count += 1
    
    if output_count > 3:
        print(f"  ... 共加载 {output_count} 个输出张量")
    
    if not output_tensors:
        print("❌ 没有找到输出数据")
        print("   提示: 请先运行simulator生成输出数据")
        return False
    
    # 打印输出数据的样本值
    if debug:
        print("\n🔍 调试：输出数据样本")
        sample_keys = ["output1_0_0", "output2_0_0", "output1_1_1", "output2_2_2"]
        for key in sample_keys:
            if key in output_tensors:
                tensor = output_tensors[key]
                # 打印前5个元素
                values = tensor.flatten()[:5].tolist()
                print(f"  {key} 前5个元素: {[f'{v:.4f}' for v in values]}")
                print(f"  {key} 均值: {tensor.mean().item():.4f}")
    
    # 计算期望的AllReduce结果
    print("\n🎯 计算期望的AllReduce结果...")
    expected_outputs = compute_expected_allreduce(input_tensors, debug=debug)
    
    if not expected_outputs:
        print("❌ 无法计算期望结果")
        return False
    
    # 打印期望结果样本
    if debug:
        print("\n🔍 调试：期望的AllReduce结果")
        sample_keys = ["output1_0_0", "output2_0_0"]
        for key in sample_keys:
            if key in expected_outputs:
                tensor = expected_outputs[key]
                values = tensor.flatten()[:5].tolist()
                print(f"  期望 {key} 前5个元素: {[f'{v:.4f}' for v in values]}")
                print(f"  期望 {key} 均值: {tensor.mean().item():.4f}")
    
    # 验证输出
    print("\n📊 验证AllReduce输出...")
    all_correct = True
    tolerance = 1e-2  # BF16精度较低，使用较大容差
    
    # 检查每个核心的输出
    verified_count = 0
    failed_count = 0
    error_samples = []  # 收集错误样本
    
    for group_id in range(NUM_GROUPS):
        for core_id in range(CORES_PER_GROUP):
            # 检查output1和output2
            for output_name in ["output1", "output2"]:
                key = f"{output_name}_{group_id}_{core_id}"
                
                if key in output_tensors and key in expected_outputs:
                    actual = output_tensors[key]
                    expected = expected_outputs[key]
                    
                    # 检查形状
                    if expected.shape != actual.shape:
                        if failed_count < 3:
                            print(f"  ❌ {key}: 形状不匹配")
                            print(f"     期望: {expected.shape}, 实际: {actual.shape}")
                        failed_count += 1
                        all_correct = False
                        continue
                    
                    # 检查数值
                    if torch.allclose(expected, actual, rtol=tolerance, atol=tolerance):
                        verified_count += 1
                    else:
                        if failed_count < 5:  # 收集前几个错误
                            diff = torch.abs(expected - actual)
                            max_diff = torch.max(diff).item()
                            mean_diff = torch.mean(diff).item()
                            
                            # 收集错误样本详情
                            error_sample = {
                                'key': key,
                                'max_diff': max_diff,
                                'mean_diff': mean_diff,
                                'expected_first': expected.flatten()[0].item(),
                                'actual_first': actual.flatten()[0].item(),
                                'expected_mean': expected.mean().item(),
                                'actual_mean': actual.mean().item()
                            }
                            error_samples.append(error_sample)
                        failed_count += 1
                        all_correct = False
    
    # 打印错误样本详情
    if error_samples and debug:
        print("\n❌ 错误样本详情:")
        for sample in error_samples[:3]:  # 显示前3个错误
            print(f"\n  {sample['key']}:")
            print(f"    最大差异: {sample['max_diff']:.6f}")
            print(f"    平均差异: {sample['mean_diff']:.6f}")
            print(f"    期望第一个元素: {sample['expected_first']:.4f}")
            print(f"    实际第一个元素: {sample['actual_first']:.4f}")
            print(f"    期望均值: {sample['expected_mean']:.4f}")
            print(f"    实际均值: {sample['actual_mean']:.4f}")
    
    print(f"\n验证统计:")
    print(f"  ✅ 通过: {verified_count} 个张量")
    if failed_count > 0:
        print(f"  ❌ 失败: {failed_count} 个张量")
    
    # 验证AllReduce的关键特性
    print("\n🔬 验证AllReduce特性...")
    
    # 1. 所有核心的相同输出应该相等
    output1_values = []
    output2_values = []
    
    for key, tensor in output_tensors.items():
        if "output1" in key:
            output1_values.append(tensor)
        elif "output2" in key:
            output2_values.append(tensor)
    
    # 检查output1的一致性
    if len(output1_values) > 1:
        all_same = all(torch.allclose(output1_values[0], v, rtol=tolerance, atol=tolerance) 
                      for v in output1_values[1:])
        if all_same:
            print("  ✅ 所有核心的output1相同（AllReduce特性）")
        else:
            print("  ❌ 核心的output1不一致")
            # 打印不一致的样本
            if debug:
                for i in range(min(3, len(output1_values))):
                    mean_val = output1_values[i].mean().item()
                    first_val = output1_values[i].flatten()[0].item()
                    print(f"    output1[{i}]: 均值={mean_val:.4f}, 第一个元素={first_val:.4f}")
            all_correct = False
    
    # 检查output2的一致性
    if len(output2_values) > 1:
        all_same = all(torch.allclose(output2_values[0], v, rtol=tolerance, atol=tolerance) 
                      for v in output2_values[1:])
        if all_same:
            print("  ✅ 所有核心的output2相同（AllReduce特性）")
        else:
            print("  ❌ 核心的output2不一致")
            # 打印不一致的样本
            if debug:
                for i in range(min(3, len(output2_values))):
                    mean_val = output2_values[i].mean().item()
                    first_val = output2_values[i].flatten()[0].item()
                    print(f"    output2[{i}]: 均值={mean_val:.4f}, 第一个元素={first_val:.4f}")
            all_correct = False
    
    # 总结
    print(f"\n{'='*50}")
    print("📊 验证结果总结:")
    if all_correct:
        print("✅ AllReduce验证通过！")
        print("   - 所有核心都收到了正确的累加结果")
        print("   - ReduceScatter和AllGather操作正确执行")
        print("   - 核间通信正常")
    else:
        print("❌ AllReduce验证失败")
        print("   请检查：")
        print("   1. ReduceScatter是否正确进行部分规约")
        print("   2. AllGather是否正确广播到所有核心")
        print("   3. 核间NoC通信是否正常")
        print("   4. 地址和数据布局是否正确")
        print("\n   提示：如果输入都是1.0，输出应该是16.0（16个核心累加）")
    print(f"{'='*50}")
    
    return all_correct

def generate_reference_output():
    """
    生成参考输出（用于debug）
    基于输入数据生成期望的AllReduce输出
    """
    print("\n🔧 生成参考输出数据（用于debug）...")
    
    # 加载输入数据
    input_tensors = {}
    in_path = Path("in_data")
    
    if not in_path.exists():
        print(f"❌ 输入目录不存在: {in_path}")
        return
    
    for pt_file in in_path.glob("*.pt"):
        tensor = load_pytorch_tensor(pt_file)
        if tensor is not None:
            input_tensors[pt_file.stem] = tensor
    
    if not input_tensors:
        print("❌ 没有找到输入数据")
        return
    
    # 计算AllReduce结果
    expected_outputs = compute_expected_allreduce(input_tensors, debug=True)
    
    if not expected_outputs:
        print("❌ 无法计算期望结果")
        return
    
    # 创建参考输出目录
    ref_dir = Path("ref_data")
    os.makedirs(ref_dir, exist_ok=True)
    
    # 保存参考输出
    for key, tensor in expected_outputs.items():
        torch.save(tensor, ref_dir / f"{key}.pt")
    
    print(f"✅ 参考输出已生成到: {ref_dir}/")
    print(f"   共生成 {len(expected_outputs)} 个输出张量")
    
    # 打印样本值
    sample_key = "output1_0_0"
    if sample_key in expected_outputs:
        tensor = expected_outputs[sample_key]
        print(f"\n   样本 {sample_key}:")
        print(f"     前5个元素: {tensor.flatten()[:5].tolist()}")
        print(f"     均值: {tensor.mean().item():.4f}")
        print(f"     如果输入都是1.0，这里应该是16.0")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='验证AllReduce操作结果')
    parser.add_argument('--in_dir', default='in_data', help='输入数据目录')
    parser.add_argument('--out_dir', default='out_data', help='输出数据目录')
    parser.add_argument('--tolerance', type=float, default=1e-2,
                       help='数值比较的容忍度（BF16默认1e-2）')
    parser.add_argument('--generate_ref', action='store_true',
                       help='生成参考输出数据')
    parser.add_argument('--no_debug', action='store_true',
                       help='禁用调试输出')
    
    args = parser.parse_args()
    
    # 切换到脚本所在目录
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    if args.generate_ref:
        # 生成参考输出
        generate_reference_output()
    else:
        # 执行验证
        result = verify_allreduce(debug=not args.no_debug)
        
        # 返回退出码
        exit(0 if result else 1)

if __name__ == "__main__":
    main()