#!/usr/bin/env python3
"""
追踪AllReduce执行过程，分析累加地址
"""

import re
from collections import defaultdict

def parse_log():
    """解析日志文件，提取vvv_drv调用信息"""
    log_file = "/data/users/jxchen/mosim_workspace/work/python/logs/npu_demo_output.log"
    
    vvv_calls = []
    pattern = re.compile(r'handle vvv_drv\([^,]+,\s*(\d+),')
    
    with open(log_file, 'r') as f:
        for line_num, line in enumerate(f, 1):
            if 'vvv_drv' in line:
                match = pattern.search(line)
                if match:
                    address = int(match.group(1))
                    vvv_calls.append({
                        'line': line_num,
                        'address': address,
                        'hex_addr': f"0x{address:08x}"
                    })
    
    return vvv_calls

def analyze_addresses(vvv_calls):
    """分析地址模式"""
    print(f"总共找到 {len(vvv_calls)} 次 vvv_drv 调用\n")
    
    # 地址映射
    SPAD0_BASE = 0x00000000
    SPAD1_BASE = 0x00100000
    STRIDE = 512  # 每行512字节
    
    # 按地址分组统计
    addr_count = defaultdict(int)
    for call in vvv_calls:
        addr_count[call['address']] += 1
    
    # 分析每个地址
    print("=== 地址累加统计 ===")
    print("地址         | SPAD | 行号 | 累加次数")
    print("-" * 50)
    
    unique_addrs = sorted(addr_count.keys())
    for addr in unique_addrs:
        count = addr_count[addr]
        
        # 判断属于哪个SPAD
        if addr >= SPAD1_BASE:
            spad = "SPAD1"
            row = (addr - SPAD1_BASE) // STRIDE
            offset = (addr - SPAD1_BASE) % STRIDE
        else:
            spad = "SPAD0"
            row = addr // STRIDE
            offset = addr % STRIDE
        
        # 只打印行起始地址（offset=0）
        if offset == 0:
            print(f"0x{addr:08x} | {spad} | {row:4d} | {count:3d}")
    
    print("\n=== 统计汇总 ===")
    print(f"不同地址数: {len(unique_addrs)}")
    print(f"总调用次数: {sum(addr_count.values())}")
    
    # 检查每个地址的累加次数
    expected_count = 15  # 每个地址应该被累加15次
    correct_count = 0
    wrong_count = 0
    
    for addr, count in addr_count.items():
        if addr % STRIDE == 0:  # 只检查行起始地址
            if count == expected_count:
                correct_count += 1
            else:
                wrong_count += 1
    
    print(f"\n累加次数检查（期望每行累加{expected_count}次）：")
    print(f"  正确: {correct_count} 行")
    print(f"  错误: {wrong_count} 行")

def trace_stages():
    """追踪每个stage的执行"""
    vvv_calls = parse_log()
    
    if not vvv_calls:
        print("没有找到vvv_drv调用")
        return
    
    NUM_CORES = 16
    NUM_STAGES = 15
    calls_per_stage = NUM_CORES
    
    print("\n=== Stage执行追踪 ===")
    
    # 假设调用是按stage顺序的
    for stage in range(NUM_STAGES):
        start_idx = stage * calls_per_stage
        end_idx = start_idx + calls_per_stage
        
        if end_idx > len(vvv_calls):
            break
        
        stage_calls = vvv_calls[start_idx:end_idx]
        
        print(f"\nStage {stage}: (调用 {start_idx}-{end_idx-1})")
        
        # 统计这个stage的地址
        stage_addrs = set()
        for call in stage_calls:
            addr = call['address']
            stage_addrs.add(addr)
        
        print(f"  涉及 {len(stage_addrs)} 个不同地址")
        
        # 显示前3个调用的详细信息
        for i, call in enumerate(stage_calls[:3]):
            addr = call['address']
            spad = "SPAD1" if addr >= 0x100000 else "SPAD0"
            row = (addr % 0x100000) // 512
            print(f"  Core {i}: 累加到 {spad} 行{row} (0x{addr:08x})")

def verify_correctness():
    """验证累加的正确性"""
    print("\n=== 正确性验证 ===")
    
    vvv_calls = parse_log()
    analyze_addresses(vvv_calls)
    
    print("\n问题诊断：")
    print("1. 如果每行只累加1次而不是15次：")
    print("   - 可能是数据在每个stage被覆盖而不是累加")
    print("   - vv_v_primitive可能没有正确执行ADD操作")
    
    print("\n2. 如果地址分布不均：")
    print("   - 可能是mask设置问题，部分核心没有执行")
    print("   - 地址计算可能有误")
    
    print("\n3. 建议的验证方法：")
    print("   - 在vv_v_primitive_pre/drv前后读取内存值")
    print("   - 使用不同初始值（如1.0, 2.0, 3.0...）追踪数据流")
    print("   - 简化为2核心测试，减少复杂度")

def main():
    print("="*60)
    print("AllReduce执行追踪分析")
    print("="*60)
    
    trace_stages()
    verify_correctness()
    
    print("\n" + "="*60)
    print("调试建议：")
    print("1. 在C代码中的vv_v_primitive_drv后添加内存读取，验证累加结果")
    print("2. 使用递增的初始值（1,2,3...）而不是全1，便于追踪")
    print("3. 添加同步点，确保每个stage完成后再继续")
    print("="*60)

if __name__ == "__main__":
    main()