#!/usr/bin/env python3
"""
调试AllReduce实现
分析ReduceScatter和AllGather的执行流程
"""

import numpy as np

# 常量定义（与C代码一致）
NUM_NODES = 16
HALF_NODES = 8

# 核心拓扑结构
core_cord = [
    0x0000, 0x0001, 0x0002, 0x0003, 0x0100, 0x0101, 0x0102, 0x0103,
    0x0200, 0x0201, 0x0202, 0x0203, 0x0300, 0x0301, 0x0302, 0x0303
]

loop = [0, 1, 2, 3, 4, 5, 6, 7, 15, 14, 13, 12, 11, 10, 9, 8]

# 计算core_rank
core_rank = [0] * NUM_NODES
for i in range(NUM_NODES):
    node_index = loop[i]
    core_rank[node_index] = i

# 计算src_cord和dst_cord
src_cord = [0] * NUM_NODES
dst_cord = [0] * NUM_NODES
for i in range(NUM_NODES):
    src_cord[i] = core_cord[loop[(core_rank[i] - 1 + NUM_NODES) % NUM_NODES]]
    dst_cord[i] = core_cord[loop[(core_rank[i] + 1 + NUM_NODES) % NUM_NODES]]

print("核心拓扑信息:")
print(f"core_rank: {core_rank}")
print(f"src_cord: {[hex(x) for x in src_cord]}")
print(f"dst_cord: {[hex(x) for x in dst_cord]}")

def simulate_reduce_scatter():
    """模拟ReduceScatter操作"""
    print("\n" + "="*60)
    print("模拟ReduceScatter操作")
    print("="*60)
    
    # 初始化数据：每个核心有一行数据，值为1
    # input1存储行0-7，input2存储行8-15
    data = np.ones((NUM_NODES, 256))  # 16行，每行256个元素，初始值都是1
    
    print("\n初始数据：")
    print(f"  input1 (SPAD0): 行0-7，每个元素值=1")
    print(f"  input2 (SPAD1): 行8-15，每个元素值=1")
    
    # 模拟15个stage的ReduceScatter
    for stage in range(NUM_NODES - 1):
        print(f"\nStage {stage}:")
        
        send_recv_info = []
        for i in range(NUM_NODES):
            # 根据C代码的索引计算
            send_index = (core_rank[i] - stage + NUM_NODES) % NUM_NODES
            recv_index = (core_rank[i] - stage - 1 + NUM_NODES) % NUM_NODES
            
            # 计算实际的行号
            send_row = send_index  # 直接对应行号
            recv_row = recv_index  # 直接对应行号
            
            # 确定数据来源（input1或input2）
            send_spad = "SPAD0(input1)" if send_index < 8 else "SPAD1(input2)"
            recv_spad = "SPAD0(input1)" if recv_index < 8 else "SPAD1(input2)"
            
            send_recv_info.append({
                'core': i,
                'send_index': send_index,
                'recv_index': recv_index,
                'send_row': send_row,
                'recv_row': recv_row,
                'send_spad': send_spad,
                'recv_spad': recv_spad
            })
        
        # 打印前几个核心的信息
        for info in send_recv_info[:3]:
            print(f"  Core {info['core']:2d}: send row {info['send_row']:2d} from {info['send_spad']:15s}, "
                  f"recv row {info['recv_row']:2d} from {info['recv_spad']:15s}")
        if len(send_recv_info) > 3:
            print(f"  ... (其余{NUM_NODES-3}个核心)")
    
    print("\n期望的ReduceScatter结果：")
    print("  每个核心负责累加一部分数据")
    print("  最终每个核心应该有部分行的累加结果")

def simulate_all_gather():
    """模拟AllGather操作"""
    print("\n" + "="*60)
    print("模拟AllGather操作")
    print("="*60)
    
    print("\n初始状态（ReduceScatter后）：")
    print("  每个核心有部分累加结果")
    
    # 模拟15个stage的AllGather
    for stage in range(NUM_NODES - 1):
        print(f"\nStage {stage}:")
        
        send_recv_info = []
        for i in range(NUM_NODES):
            # 根据C代码的索引计算
            send_index = (core_rank[i] - stage + 1 + NUM_NODES) % NUM_NODES
            recv_index = (core_rank[i] - stage + NUM_NODES) % NUM_NODES
            
            # 计算实际的行号
            send_row = send_index
            recv_row = recv_index
            
            # 确定数据位置
            send_spad = "SPAD0(input1)" if send_index < 8 else "SPAD1(input2)"
            recv_spad = "SPAD0(input1)" if recv_index < 8 else "SPAD1(input2)"
            
            send_recv_info.append({
                'core': i,
                'send_index': send_index,
                'recv_index': recv_index,
                'send_row': send_row,
                'recv_row': recv_row,
                'send_spad': send_spad,
                'recv_spad': recv_spad
            })
        
        # 打印前几个核心的信息
        for info in send_recv_info[:3]:
            print(f"  Core {info['core']:2d}: send row {info['send_row']:2d} from {info['send_spad']:15s}, "
                  f"recv row {info['recv_row']:2d} from {info['recv_spad']:15s}")
        if len(send_recv_info) > 3:
            print(f"  ... (其余{NUM_NODES-3}个核心)")
    
    print("\n期望的AllGather结果：")
    print("  所有核心都应该有完整的累加结果")
    print("  如果输入都是1，每个元素应该是16")

def analyze_address_mapping():
    """分析地址映射关系"""
    print("\n" + "="*60)
    print("地址映射分析")
    print("="*60)
    
    print("\n数据布局：")
    print("  input1 (SPAD0): 基地址=0x00000000")
    print("  input2 (SPAD1): 基地址=0x00100000")
    print("  byte_stride1_u = 32 * 16 = 512 bytes per row")
    
    print("\n行号到地址的映射：")
    for i in range(NUM_NODES):
        if i < 8:
            addr = f"0x00000000 + {i} * 512 = 0x{i * 512:08x}"
            print(f"  行{i:2d}: input1, 偏移={i}行, 地址={addr}")
        else:
            addr = f"0x00100000 + {i-8} * 512 = 0x{0x00100000 + (i-8) * 512:08x}"
            print(f"  行{i:2d}: input2, 偏移={i-8}行, 地址={addr}")

def verify_allreduce_logic():
    """验证AllReduce逻辑的正确性"""
    print("\n" + "="*60)
    print("AllReduce逻辑验证")
    print("="*60)
    
    # 模拟简化的AllReduce
    print("\n简化的AllReduce过程：")
    print("1. 初始状态：")
    print("   - 16个核心，每个核心看到相同的input1[8,256]和input2[8,256]")
    print("   - 所有输入值都是1.0")
    
    print("\n2. ReduceScatter阶段：")
    print("   - 每个核心负责累加一部分数据")
    print("   - 理论上应该进行部分规约")
    
    print("\n3. AllGather阶段：")
    print("   - 广播累加结果到所有核心")
    print("   - 最终每个核心都有完整结果")
    
    print("\n4. 期望结果：")
    print("   - 如果实现正确，每个元素应该是16（16个核心的累加）")
    print("   - 但如果只是在input1和input2之间传递，可能只是2")
    
    # 检查可能的问题
    print("\n可能的问题：")
    print("1. ReduceScatter可能没有正确累加所有核心的数据")
    print("2. 数据映射可能有误（行号到SPAD地址的转换）")
    print("3. 累加操作可能只在部分数据上执行")
    print("4. AllGather可能没有正确广播到所有核心")

def main():
    """主函数"""
    print("="*60)
    print("AllReduce调试分析")
    print("="*60)
    
    # 分析各个组件
    analyze_address_mapping()
    simulate_reduce_scatter()
    simulate_all_gather()
    verify_allreduce_logic()
    
    print("\n" + "="*60)
    print("建议的调试步骤：")
    print("="*60)
    print("1. 检查ReduceScatter的累加是否正确执行")
    print("2. 验证数据索引计算（send_index, recv_index）")
    print("3. 确认vv_v_primitive操作是否正确累加")
    print("4. 检查AllGather的广播逻辑")
    print("5. 验证最终输出地址是否正确")

if __name__ == "__main__":
    main()