#!/usr/bin/env python3
"""
AllReduce配置生成脚本
生成用于测试test_allreduce函数的输入输出配置文件
根据修改后的test_allreduce.c：dim1=8而不是4
"""

import json
from typing import List, Dict, Any, NamedTuple
import torch
import os
from pathlib import Path

###############################################################################
# Private Function Tools                                                      #
###############################################################################

WORD_BITS = 256
WORD_BYTES = WORD_BITS // 8
NUM_CORES = 16  # 总核心数

def min_strides(shape, width):
    (dim2, dim1, dim0) = shape
    dim0a = WORD_BITS // width
    dim0b = (dim0 + dim0a - 1) // dim0a
    stride0b = 1
    stride1 = dim0b
    stride2 = stride1 * dim1
    return (stride2, stride1, stride0b)

def dtype_convert(dtype="INT4"):
    _map = {
        "INT4"      :   (4, torch.int8      ), # use int8 for calculation
        "INT8"      :   (8, torch.int8      ),
        "INT16"     :   (16,torch.int16     ),
        "INT32"     :   (32,torch.int32     ),
        "FP16"      :   (16,torch.float16   ),
        "FP32"      :   (32,torch.float32   ),
        "BF16"      :   (16,torch.bfloat16  )
    }
    
    if dtype not in _map:
        raise ValueError(f"Unsupported dtype: {dtype}")
    return _map[dtype]

def create_min_tensor(name: str, group_id: int, core_id: int, address: int, shape: tuple, dtype: str) -> Dict[str, Any]:
    width, _ = dtype_convert(dtype)
    (dim2, dim1, dim0) = shape
    stride2, stride1, stride0b = min_strides(shape, width)
    template = {
        "name": f"{name}_{group_id}_{core_id}",
        "address": f"0x{address:08x}",
        "datatype": dtype,
        "dimension": {
            "dim0": dim0,
            "dim1": dim1,
            "dim2": dim2
        },
        "stride": {
            "stride0b": stride0b,
            "stride1": stride1,
            "stride2": stride2
        }
    }
    return template

def mask2core_id(mask: List[int]) -> List[tuple]:
    config = []
    for group_id in range(4):
        for core_id in range(4):
            if mask[group_id] & (1 << core_id):
                config.append((group_id, core_id))
    return config

def get_all_cores() -> List[tuple]:
    """获取所有16个核心的坐标"""
    cores = []
    for group_id in range(4):
        for core_id in range(4):
            cores.append((group_id, core_id))
    return cores

TensorDescriptor = NamedTuple("TensorDescriptor", [
    ("name", str),
    ("address", int),
    ("shape", tuple),
    ("dtype", str),
])

###############################################################################
# Configurations                                                              #
###############################################################################

# Scratchpad地址定义（与C代码一致）
SCRATCHPAD0_ADDR = 0x00000000
SCRATCHPAD1_ADDR = 0x00100000
SCRATCHPAD2_ADDR = 0x00200000
SCRATCHPAD3_ADDR = 0x00300000

def generate_allreduce_configs():
    """生成AllReduce测试的配置"""
    
    # 根据修改后的C代码：
    # input1和input2的配置: dim0=256, dim1=8, dim2=1, BF16类型
    # byte_stride1_u = 32 * 16 = 512
    # byte_stride2_u = 32 * 128 = 4096
    
    in_configs = []
    out_configs = []
    
    all_cores = get_all_cores()
    
    # 输入配置：每个核心都有相同的input1和input2
    # 不需要特殊处理，每个核心看到完整的tensor
    for group_id, core_id in all_cores:
        # Input1 - SCRATCHPAD0
        # 每个核心都有完整的input1 (256x8x1)
        config1 = create_min_tensor(
            name="input1",
            group_id=group_id,
            core_id=core_id,
            address=SCRATCHPAD0_ADDR,
            shape=(1, 8, 256),  # dim2=1, dim1=8, dim0=256
            dtype="BF16"
        )
        in_configs.append(config1)
        
        # Input2 - SCRATCHPAD1
        # 每个核心都有完整的input2 (256x8x1)
        config2 = create_min_tensor(
            name="input2",
            group_id=group_id,
            core_id=core_id,
            address=SCRATCHPAD1_ADDR,
            shape=(1, 8, 256),  # dim2=1, dim1=8, dim0=256
            dtype="BF16"
        )
        in_configs.append(config2)
    
    # 输出配置：AllReduce后，每个核心的输出
    # 输出应该在原地址（input1和input2的位置）
    for group_id, core_id in all_cores:
        # Output1 - 在SCRATCHPAD0
        config_out1 = create_min_tensor(
            name="output1",
            group_id=group_id,
            core_id=core_id,
            address=SCRATCHPAD0_ADDR,
            shape=(1, 8, 256),  # dim2=1, dim1=8, dim0=256
            dtype="BF16"
        )
        out_configs.append(config_out1)
        
        # Output2 - 在SCRATCHPAD1
        config_out2 = create_min_tensor(
            name="output2",
            group_id=group_id,
            core_id=core_id,
            address=SCRATCHPAD1_ADDR,
            shape=(1, 8, 256),  # dim2=1, dim1=8, dim0=256
            dtype="BF16"
        )
        out_configs.append(config_out2)
    
    return in_configs, out_configs

def generate_test_input_data():
    """
    生成测试输入数据（可选，用于debug）
    参考gen_input.py的格式
    """
    print("\n🔧 生成测试输入数据（用于debug）...")
    
    # 创建数据目录
    os.makedirs("in_data", exist_ok=True)
    
    # 为简化测试，每个核心的input1和input2使用相同的值
    # 但不同核心可以有不同的值来验证AllReduce
    
    all_cores = get_all_cores()
    
    for group_id, core_id in all_cores:
        # 创建input1张量 - 每个核心使用相同的input1
        # 为了测试，可以让input1的每个元素为1.0
        tensor1 = torch.ones((1, 8, 256), dtype=torch.bfloat16) * 1.0
        torch.save(tensor1, f"in_data/input1_{group_id}_{core_id}.pt")
        
        # 创建input2张量 - 每个核心使用相同的input2
        # 为了测试，可以让input2的每个元素为2.0
        tensor2 = torch.ones((1, 8, 256), dtype=torch.bfloat16) * 1.0
        torch.save(tensor2, f"in_data/input2_{group_id}_{core_id}.pt")
    
    print(f"✅ 测试输入数据已生成到: in_data/")
    print(f"   - input1: 所有元素为1.0")
    print(f"   - input2: 所有元素为1.0")

def main():
    """主函数"""
    print("🔧 生成AllReduce测试配置...")
    print("   基于修改后的test_allreduce.c (dim1=8)")
    
    # 生成配置
    in_configs, out_configs = generate_allreduce_configs()
    
    # 保存输入配置
    in_file = "input_tensor.json"
    with open(in_file, "w", encoding='utf-8') as f:
        json.dump(in_configs, f, indent=4, ensure_ascii=False)
    print(f"\n✅ 输入配置已保存到: {in_file}")
    print(f"   配置数量: {len(in_configs)} (每个核心有input1和input2)")
    
    # 保存输出配置
    out_file = "output_tensor.json"
    with open(out_file, "w", encoding='utf-8') as f:
        json.dump(out_configs, f, indent=4, ensure_ascii=False)
    print(f"✅ 输出配置已保存到: {out_file}")
    print(f"   配置数量: {len(out_configs)} (每个核心有output1和output2)")
    
    print("\n📊 配置总结:")
    print(f"  - 核心数量: {NUM_CORES}")
    print(f"  - 数据类型: BF16")
    print(f"  - 输入张量形状: (1, 8, 256) 每个核心")
    print(f"  - Input1地址: 0x{SCRATCHPAD0_ADDR:08x}")
    print(f"  - Input2地址: 0x{SCRATCHPAD1_ADDR:08x}")
    print(f"  - 每个核心都有相同的完整input1和input2")
    
    # 询问是否生成测试数据
    print("\n" + "="*50)
    print("提示: 可以使用以下命令生成输入数据:")
    print("  1. 使用gen_input.py生成随机数据: python gen_input.py")
    print("  2. 或运行此脚本的测试数据生成: 取消下面注释")
    print("="*50)
    
    # 如果需要生成测试数据，取消下面的注释
    generate_test_input_data()

if __name__ == "__main__":
    main()