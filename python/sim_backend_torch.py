#################################################################################################################
# HUJIYONG                                                                                                      # 
# 2025.07.26                                                                                                    #
#################################################################################################################
# Something you need to know before take over this code:                                                        #
# 1. Treat reminder of a 256b-word as undefined. So don't try operations litter than 256b-word                  #
# 2. Treat INT4 as torch.int8                                                                                   #
#                                                                                                               #
#################################################################################################################
# tensor(shape=(dim2, dim1, dim0), byte_stride=(stride2, stride1, stride0b))                                    #
# word_size = 256                                                                                               #
# dim0a = 256 / elem_width                                                                                      #
# dim0b = (dim0 + dim0a - 1) // dim0a                                                                           #
# fix stride0b to 1                                                                                             #
# total tensor size = dim2 * byte_stride2                                                                       #
#################################################################################################################





import torch
from collections import namedtuple
from dataclasses import dataclass
from typing import Tuple, Optional







MEM_CONFIG_TYPE = namedtuple("MEM_CONFIG", ["base_addr", "word_bytes", "num_words"])


@dataclass
class TensorDescriptor:
    """Encapsulates all tensor metadata for operations"""
    addr: int
    shape: Tuple[int, int, int]
    byte_stride: Tuple[int, int, int]  # Renamed from stride to byte_stride
    dtype: str
    
    def __post_init__(self):
        """Validate tensor configuration after initialization"""
        assert_valid_tensor_config(self.addr, self.shape, self.byte_stride, self.dtype)
    
    def __repr__(self):
        return f"TensorDescriptor(addr=0x{self.addr:08x}, shape={self.shape}, byte_stride={self.byte_stride}, dtype={self.dtype})"
    
    # Shape properties
    @property
    def dim0(self) -> int:
        """Get dim0 (innermost dimension)"""
        return self.shape[2]
    
    @property
    def dim1(self) -> int:
        """Get dim1 (middle dimension)"""
        return self.shape[1]
    
    @property
    def dim2(self) -> int:
        """Get dim2 (outermost dimension)"""
        return self.shape[0]
    
    # Byte stride properties
    @property
    def byte_stride0b(self) -> int:
        """Get byte_stride0b (innermost stride in bytes)"""
        return self.byte_stride[2]
    
    @property
    def byte_stride1(self) -> int:
        """Get byte_stride1 (middle stride in bytes)"""
        return self.byte_stride[1]
    
    @property
    def byte_stride2(self) -> int:
        """Get byte_stride2 (outermost stride in bytes)"""
        return self.byte_stride[0]
    
    # Word stride properties (stride in 256-bit words)
    @property
    def word_stride0b(self) -> int:
        """Get word_stride0b (innermost stride in 256-bit words)"""
        return self.byte_stride0b // CONFIG.WORD_BYTES
    
    @property
    def word_stride1(self) -> int:
        """Get word_stride1 (middle stride in 256-bit words)"""
        return self.byte_stride1 // CONFIG.WORD_BYTES
    
    @property
    def word_stride2(self) -> int:
        """Get word_stride2 (outermost stride in 256-bit words)"""
        return self.byte_stride2 // CONFIG.WORD_BYTES
    
    # Derived properties
    @property
    def dim0a(self) -> int:
        """Get dim0a (elements per word)"""
        width, _ = dtype_convert(self.dtype)
        return CONFIG.WORD_BITS // width
    
    @property
    def dim0b(self) -> int:
        """Get dim0b (number of words in dim0)"""
        return (self.dim0 + self.dim0a - 1) // self.dim0a
    
    @property
    def element_count(self) -> int:
        """Total number of elements in tensor"""
        return self.dim0 * self.dim1 * self.dim2
    
    @property
    def byte_size(self) -> int:
        """Total bytes needed for tensor storage (considering stride)"""
        return self.dim2 * self.byte_stride2
    
    @property
    def word_shape(self) -> Tuple[int, int, int]:
        """Get shape in words (dim2, dim1, dim0b)"""
        return (self.dim2, self.dim1, self.dim0b)
    
    @property
    def word_stride(self) -> Tuple[int, int, int]:
        """Get stride in 256-bit words"""
        return (self.word_stride2, self.word_stride1, self.word_stride0b)
    
    # Utility methods
    def copy_with(self, **kwargs) -> 'TensorDescriptor':
        """Create a copy with modified fields"""
        data = {
            'addr': self.addr,
            'shape': self.shape,
            'byte_stride': self.byte_stride,
            'dtype': self.dtype
        }
        data.update(kwargs)
        return TensorDescriptor(**data)

class CONFIG:
    # global configs
    WORD_BITS  = 256
    WORD_BYTES = WORD_BITS // 8 # Bytes

    # memorys
    SPAD_BASE_ADDR = [  
        0x0000_0000,
        0x0010_0000,      
        0x0020_0000,
        0x0030_0000,  
    ]
    CIMC_BASE_ADDR  = 0x0040_0000
    DRAM_BASE_ADDR = 0x1000_0000

    NUM_SPAD = 4
    NUM_CIMC_PAGE = 16
    
    SPAD_WORD_NUM = 2048 # words
    CIMC_WORD_NUM = 256 # words
    DRAM_WORD_NUM = 4 * 1024 * 1024

    NUM_CORES = 16


def dtype_convert(dtype="INT4"):
    _map = {
        "INT4"      :   (4, torch.int8      ), # use int8 for calculation
        "INT8"      :   (8, torch.int8      ),
        "INT16"     :   (16,torch.int16     ),
        "INT32"     :   (32,torch.int32     ),
        "FP16"      :   (16,torch.float16   ),
        "FP32"      :   (32,torch.float32   ),
        "BF16"      :   (16,torch.bfloat16  )
    }

    if dtype not in _map:
        raise ValueError(f"Unsupported dtype: {dtype}")
    return _map[dtype]

    

###########################################################################
# Private Function Tools                                                  #
###########################################################################

def assert_valid_tensor_config(addr, shape, byte_stride, dtype="INT4"):
    '''
    addr        : start address of tensor, in bytes
    shape       : shape of tensor, (dim2, dim1, dim0)
    byte_stride : stride of tensor, in bytes, (stride2, stride1, 1) 
    dtype       : INT4/8/16/32, FP16/32, BF16
    '''

    (dim2, dim1, dim0) = shape
    (byte_stride2, byte_stride1, byte_stride0b) = byte_stride
    width, _ = dtype_convert(dtype)
    dim0a = CONFIG.WORD_BITS // width
    dim0b = (dim0 + dim0a - 1) // dim0a


    tensor_info = f"addr = {addr}, shape = {shape}, byte_stride = {byte_stride}, dtype = {dtype}"

    error_list = [
        (addr % CONFIG.WORD_BYTES != 0                   , f"Address of Valid Tensor Should Align to {CONFIG.WORD_BITS}bits({CONFIG.WORD_BYTES} Bytes)"    ),
        (len(shape) != 3                                 , f"Valid Tensor should have 3 dimnesions"                                                        ),
        (dim0 * dim1 * dim2 == 0                         , f"Shape of Valid Tensor should not contains 0"                                                  ),
        (len(byte_stride) != 3                           , f"Valid Tensor should have 3 dimnesions of stride"                                              ),
        (byte_stride0b != CONFIG.WORD_BYTES              , f"Valid Tensor Should Fix Stride0b to {CONFIG.WORD_BYTES} Bytes"                                ),
        (byte_stride1 % CONFIG.WORD_BYTES != 0           , f"Stride1 Should Align to {CONFIG.WORD_BITS}bits({CONFIG.WORD_BYTES} Bytes)"                    ),
        (byte_stride2 % CONFIG.WORD_BYTES != 0           , f"Stride2 Should Align to {CONFIG.WORD_BITS}bits({CONFIG.WORD_BYTES} Bytes)"                    ),
        (byte_stride1  <  dim0b * CONFIG.WORD_BYTES      , f"byte_stride_dim1 < dim0b * {CONFIG.WORD_BYTES} Bytes"                                         ),
        (byte_stride2  <  dim1 * byte_stride1            , f"byte_stride2 < dim1 * byte_stride1"                                                           ),
    ]

    for error in error_list:
        if error[0]:
            raise ValueError(f"Tensor({tensor_info}) is invalid, {error[1]}")

def int8_to_int4(x: torch.Tensor) -> torch.Tensor:
    original_shape = x.shape
    x_flat = x.view(-1) # Flatten to 1D
    x_u8 = x_flat.view(torch.uint8)
    
    hi4 = ((x_u8 >> 4) & 0x0F).to(torch.int8)
    lo4 = (x_u8 & 0x0F).to(torch.int8)
    hi4 = torch.where(hi4 > 7, hi4 - 16, hi4)
    lo4 = torch.where(lo4 > 7, lo4 - 16, lo4)
    
    result = torch.stack([lo4, hi4], dim=-1).view(-1)
    new_shape = list(original_shape)
    new_shape[-1] = original_shape[-1] * 2
    return result.view(*new_shape)

def int4_to_int8(x: torch.Tensor) -> torch.Tensor:
    original_shape = x.shape
    assert original_shape[-1] % 2 == 0, f"Last Dimension Must Be Even For INT4 Packing, Got {original_shape[-1]}"
    
    new_shape = list(original_shape)
    new_shape[-1] = original_shape[-1] // 2
    new_shape.append(2)
    x_reshaped = x.view(*new_shape)
    
    lo4 = x_reshaped[..., 0].to(torch.uint8) & 0x0F
    hi4 = x_reshaped[..., 1].to(torch.uint8) & 0x0F
    
    tout_u8 = ((hi4 << 4) | lo4).to(torch.uint8)
    
    result_shape = list(original_shape)
    result_shape[-1] = original_shape[-1] // 2
    return tout_u8.view(*result_shape)

def bytes2tensor(tensor_bytes: torch.Tensor, shape: tuple, dtype: str) -> torch.Tensor:
    '''
    tensor_bytes: bytes of tensor
    shape: shape of tensor
    dtype: dtype of tensor, INT4/8/16/32, FP16/32, BF16
    '''
    width, torch_dtype = dtype_convert(dtype)
    (dim2, dim1, dim0) = shape
    dim0a = CONFIG.WORD_BITS // width
    dim0b = (dim0 + dim0a - 1) // dim0a
    rem_dim0 = dim0 % dim0a

    if width != 4: # INT8/16/32/FP16/BF16
        tensor_out = tensor_bytes.view(torch_dtype)
        tensor_out = tensor_out.view(dim2, dim1, dim0b * dim0a)
    else: # INT4
        tensor_out = int8_to_int4(tensor_bytes.view(torch.int8))
        tensor_out = tensor_out.view(dim2, dim1, dim0b * dim0a)
    
    if rem_dim0 != 0:
        tensor_out = tensor_out[:, :, 0: dim0]

    return tensor_out

def tensor2bytes(tensor_in: torch.Tensor, shape: tuple, dtype: str) -> torch.Tensor:
    '''
    tensor: tensor to be converted to bytes
    tensor_bytes should shape as (dim2, dim1, dim0b * dim0a)
    '''
    width, _ = dtype_convert(dtype)
    (dim2, dim1, dim0) = shape
    dim0a = CONFIG.WORD_BITS // width
    dim0b = (dim0 + dim0a - 1) // dim0a
    rem_dim0 = dim0 % dim0a

    # zero-fill to 256b-word
    if rem_dim0 != 0:
        full_dim0 = dim0b * dim0a
        tensor_in = torch.cat([tensor_in, torch.zeros(dim2, dim1, full_dim0 - dim0, dtype=tensor_in.dtype)], dim=-1)
    else:
        full_dim0 = dim0b * dim0a

    dim0 = full_dim0

    if not tensor_in.is_contiguous():
        tensor_in = tensor_in.contiguous()

    if width != 4:
        tensor_bytes = tensor_in.view(torch.uint8).view(dim2, dim1, dim0b * CONFIG.WORD_BYTES)
    else: # INT4
        tensor_in = int4_to_int8(tensor_in)
        tensor_bytes = tensor_in.view(dim2, dim1, dim0b * CONFIG.WORD_BYTES)

    return tensor_bytes
        

def scalar_from_int(scalar_in: int, dtype_scalar: str) -> float:
    '''
    Convert scalar from int representation to actual value based on dtype
    scalar_in: integer representation of the scalar value
    dtype_scalar: data type of the scalar ("INT4", "INT8", "INT16", "INT32", "FP16", "FP32", "BF16")
    Returns: the scalar value as float for computation
    '''
    if dtype_scalar == "FP32":
        # Interpret int bits as FP32
        scalar_value = torch.tensor(scalar_in, dtype=torch.uint32).view(torch.float32).item()
    elif dtype_scalar == "FP16":
        # Interpret lower 16 bits as FP16
        scalar_value = torch.tensor(scalar_in & 0xFFFF, dtype=torch.uint16).view(torch.float16).item()
    elif dtype_scalar == "BF16":
        # Interpret lower 16 bits as BF16
        scalar_value = torch.tensor(scalar_in & 0xFFFF, dtype=torch.uint16).view(torch.bfloat16).item()
    elif dtype_scalar == "INT4":
        # INT4 is signed 4-bit integer (-8 to 7)
        scalar_value = scalar_in & 0xF  # Get lower 4 bits
        if scalar_value >= 8:
            scalar_value -= 16  # Convert to signed
        scalar_value = max(-8, min(7, scalar_value))
    elif dtype_scalar == "INT8":
        # INT8 is signed 8-bit integer
        scalar_value = scalar_in & 0xFF  # Get lower 8 bits
        if scalar_value >= 128:
            scalar_value -= 256  # Convert to signed
    elif dtype_scalar == "INT16":
        # INT16 is signed 16-bit integer
        scalar_value = scalar_in & 0xFFFF  # Get lower 16 bits
        if scalar_value >= 32768:
            scalar_value -= 65536  # Convert to signed
    elif dtype_scalar == "INT32":
        # INT32 is signed 32-bit integer
        scalar_value = scalar_in & 0xFFFFFFFF  # Get lower 32 bits
        if scalar_value >= 2147483648:
            scalar_value -= 4294967296  # Convert to signed
    else:
        raise ValueError(f"Unsupported scalar dtype: {dtype_scalar}")
    
    return float(scalar_value)
    
###########################################################################
# Simulator Interfaces                                                    #
###########################################################################



class MEM_TYPE:
    def __init__(self, base_addr: int, word_num: int, name: str = "DEFAULT_NAME"):
        self.base_addr = base_addr
        self.word_num  = word_num
        self.name      = name

        self.byte_num = self.word_num * CONFIG.WORD_BYTES

        self.memory = torch.zeros(self.word_num * CONFIG.WORD_BYTES, dtype=torch.uint8)
    
    def contain_tensor(self, tensor: TensorDescriptor) -> bool:
        '''
        check if the tensor is in the memory
        '''
        (dim2, dim1, dim0) = tensor.shape
        (byte_stride2, byte_stride1, byte_stride0b) = tensor.byte_stride

        start_offset = tensor.addr - self.base_addr
        tensor_end_offset = start_offset + dim2 * byte_stride2

        if start_offset < 0 or start_offset >= self.byte_num or tensor_end_offset > self.byte_num:
            return False
        else:
            return True
    

    def read_bytes(self, offset, word_shape, byte_stride) -> torch.Tensor:
        '''
        offset      : start of the bytes
        word_shape  : shape by words, (dim2, dim1, dim0b)
        byte_stride : stride of tensor, in bytes, (stride2, stride1, 1) 
        '''
        (dim2, dim1, dim0b) = word_shape
        (byte_stride2, byte_stride1, byte_stride0b) = byte_stride
        
        # create a tensor to store the bytes
        tensor_bytes = torch.zeros(dim2, dim1, dim0b * CONFIG.WORD_BYTES, dtype=torch.uint8)

        # read according to strides
        for dim2_idx in range(dim2):
            for dim1_idx in range(dim1):
                for dim0b_idx in range(dim0b):
                    byte_offset = offset + dim2_idx * byte_stride2 + dim1_idx * byte_stride1 + dim0b_idx * byte_stride0b
                    tensor_bytes[dim2_idx, dim1_idx, dim0b_idx * CONFIG.WORD_BYTES: (dim0b_idx + 1) * CONFIG.WORD_BYTES] = self.memory[byte_offset:byte_offset + CONFIG.WORD_BYTES]
        
        return tensor_bytes

    def write_bytes(self, tensor_bytes: torch.Tensor, offset: int, word_shape: tuple, byte_stride: tuple):
        '''
        Write bytes to memory according to strides
        tensor_bytes: tensor containing bytes to write, shape (dim2, dim1, dim0b * WORD_BYTES)
        offset      : start offset in bytes
        word_shape  : shape by words, (dim2, dim1, dim0b)
        byte_stride : stride of tensor, in bytes, (stride2, stride1, stride0b)
        '''
        (dim2, dim1, dim0b) = word_shape
        (byte_stride2, byte_stride1, byte_stride0b) = byte_stride
        
        # Verify input tensor shape
        expected_shape = (dim2, dim1, dim0b * CONFIG.WORD_BYTES)
        assert tensor_bytes.shape == expected_shape, f"Expected tensor shape {expected_shape}, got {tensor_bytes.shape}"
        
        # Write according to strides
        for dim2_idx in range(dim2):
            for dim1_idx in range(dim1):
                for dim0b_idx in range(dim0b):
                    byte_offset = offset + dim2_idx * byte_stride2 + dim1_idx * byte_stride1 + dim0b_idx * byte_stride0b
                    # Extract the word from tensor_bytes and write to memory
                    word_start = dim0b_idx * CONFIG.WORD_BYTES
                    word_end = (dim0b_idx + 1) * CONFIG.WORD_BYTES
                    self.memory[byte_offset:byte_offset + CONFIG.WORD_BYTES] = tensor_bytes[dim2_idx, dim1_idx, word_start:word_end]

    def read_tensor(self, tensor: TensorDescriptor) -> torch.Tensor:
        '''
        read the tensor from the memory
        '''
        if not self.contain_tensor(tensor):
            raise ValueError(f"Tensor Address is out of range: {tensor}")

        width, torch_dtype = dtype_convert(tensor.dtype)
        (dim2, dim1, dim0) = tensor.shape
        (byte_stride2, byte_stride1, byte_stride0b) = tensor.byte_stride

        dim0a = CONFIG.WORD_BITS // width
        dim0b = (dim0 + dim0a - 1) // dim0a
        rem_dim0 = dim0 % dim0a

        start_offset = tensor.addr - self.base_addr
        tensor_end_offset = start_offset + dim2 * byte_stride2

        # 1. first read out the bytes data of tensor
        # 2. then explian the bytes data as type of torch_dtype
        tensor_bytes = self.read_bytes(
            start_offset, 
            (dim2, dim1, dim0b), 
            (byte_stride2, byte_stride1, byte_stride0b)
        )  
        tensor_out = bytes2tensor(tensor_bytes, tensor.shape, tensor.dtype)

        return tensor_out
    

    def write_tensor(self, tensor_in: torch.Tensor, tensor_desc: TensorDescriptor):
        '''
        write the tensor to memory
        '''
        if not self.contain_tensor(tensor_desc):
            raise ValueError(f"Tensor Address is out of range: {tensor_desc}")
        
        width, torch_dtype = dtype_convert(tensor_desc.dtype)
        (dim2, dim1, dim0) = tensor_desc.shape
        (byte_stride2, byte_stride1, byte_stride0b) = tensor_desc.byte_stride

        dim0a = CONFIG.WORD_BITS // width
        dim0b = (dim0 + dim0a - 1) // dim0a
        rem_dim0 = dim0 % dim0a

        start_offset = tensor_desc.addr - self.base_addr
        tensor_end_offset = start_offset + dim2 * byte_stride2

        # 1. first convert tensor types to bytes
        # 2. write bytes to memory according to strides
        tensor_bytes = tensor2bytes(tensor_in, tensor_desc.shape, tensor_desc.dtype)
        self.write_bytes(
            tensor_bytes, start_offset, 
            (dim2, dim1, dim0b), 
            (byte_stride2, byte_stride1, byte_stride0b)
        )
    
    
    def __repr__(self) -> str:
        return f"MEM_TYPE(name={self.name}, base_addr={self.base_addr}, word_num={self.word_num})"


class GOLDEN_VM_CORE: # Golden Virtual Machine
    def __init__(self):
        self.lmem_list = []
        self.dram_list = []

        for i in range(CONFIG.NUM_SPAD):
            self.lmem_list.append(
                MEM_TYPE(
                    CONFIG.SPAD_BASE_ADDR[i], 
                    CONFIG.SPAD_WORD_NUM, 
                    f"SPAD[{i}]"
                )
            )

        for i in range(CONFIG.NUM_CIMC_PAGE):
            self.lmem_list.append(
                MEM_TYPE(
                    CONFIG.CIMC_BASE_ADDR + i * CONFIG.CIMC_WORD_NUM * CONFIG.WORD_BYTES,
                    CONFIG.CIMC_WORD_NUM,
                    f"CIMC_PAGE[{i}]"
                )
            )

        self.dram_list.append(
            MEM_TYPE(
                CONFIG.DRAM_BASE_ADDR,
                CONFIG.DRAM_WORD_NUM,
                f"DRAM"
            )
        )
    
    

    def read_tensor(self, tensor: TensorDescriptor) -> torch.Tensor:
        for lmem in self.lmem_list:
            if lmem.contain_tensor(tensor):
                return lmem.read_tensor(tensor)
        for dram in self.dram_list:
            if dram.contain_tensor(tensor):
                return dram.read_tensor(tensor)
        raise ValueError(f"Tensor Address is out of range: {tensor}")
    

    def write_tensor(self, tensor_in: torch.Tensor, tensor_desc: TensorDescriptor):
        for lmem in self.lmem_list:
            if lmem.contain_tensor(tensor_desc):
                lmem.write_tensor(tensor_in, tensor_desc)
                return
        for dram in self.dram_list:
            if dram.contain_tensor(tensor_desc):
                dram.write_tensor(tensor_in, tensor_desc)
                return
        raise ValueError(f"Tensor Address is out of range: {tensor_desc}")
    

    def _is_in_lmem(self, tensor: TensorDescriptor) -> bool:
        '''Check if tensor is in local memory (SPAD or CIMC)'''
        for lmem in self.lmem_list:
            if lmem.contain_tensor(tensor):
                return True
        return False
    
    def _is_in_gmem(self, tensor: TensorDescriptor) -> bool:
        '''Check if tensor is in global memory (DRAM)'''
        for dram in self.dram_list:
            if dram.contain_tensor(tensor):
                return True
        return False

    def operation_load(self, tensor_gmem: TensorDescriptor, tensor_lmem: TensorDescriptor):
        '''Load tensor from global memory to local memory'''
        error_list = [
            (not self._is_in_gmem(tensor_gmem), 
             f"Source tensor must be in global memory (DRAM)"),
            (not self._is_in_lmem(tensor_lmem), 
             f"Destination tensor must be in local memory (SPAD/CIMC)"),
            (tensor_gmem.shape != tensor_lmem.shape, 
             f"Shape mismatch: source {tensor_gmem.shape} vs destination {tensor_lmem.shape}"),
            (tensor_gmem.dtype != tensor_lmem.dtype, 
             f"Dtype mismatch: source {tensor_gmem.dtype} vs destination {tensor_lmem.dtype}"),
        ]
        
        for error in error_list:
            if error[0]:
                raise ValueError(f"operation_load invalid: {error[1]}")
        
        # Read from global memory and write to local memory
        data = self.read_tensor(tensor_gmem)
        self.write_tensor(data, tensor_lmem)

    def operation_store(self, tensor_lmem: TensorDescriptor, tensor_gmem: TensorDescriptor):
        '''Store tensor from local memory to global memory'''
        error_list = [
            (not self._is_in_lmem(tensor_lmem), 
             f"Source tensor must be in local memory (SPAD/CIMC)"),
            (not self._is_in_gmem(tensor_gmem), 
             f"Destination tensor must be in global memory (DRAM)"),
            (tensor_lmem.shape != tensor_gmem.shape, 
             f"Shape mismatch: source {tensor_lmem.shape} vs destination {tensor_gmem.shape}"),
            (tensor_lmem.dtype != tensor_gmem.dtype, 
             f"Dtype mismatch: source {tensor_lmem.dtype} vs destination {tensor_gmem.dtype}"),
        ]
        
        for error in error_list:
            if error[0]:
                raise ValueError(f"operation_store invalid: {error[1]}")
        
        # Read from local memory and write to global memory
        data = self.read_tensor(tensor_lmem)
        self.write_tensor(data, tensor_gmem)

    def operation_move(self, tensor_in: TensorDescriptor, tensor_out: TensorDescriptor):
        '''Move tensor from one location to another (both must be in local memory)'''
        error_list = [
            (not self._is_in_lmem(tensor_in), 
             f"Source tensor must be in local memory (SPAD/CIMC)"),
            (not self._is_in_lmem(tensor_out), 
             f"Destination tensor must be in local memory (SPAD/CIMC)"),
            (tensor_in.shape != tensor_out.shape, 
             f"Shape mismatch: source {tensor_in.shape} vs destination {tensor_out.shape}"),
            (tensor_in.dtype != tensor_out.dtype, 
             f"Dtype mismatch: source {tensor_in.dtype} vs destination {tensor_out.dtype}"),
        ]
        
        for error in error_list:
            if error[0]:
                raise ValueError(f"operation_move invalid: {error[1]}")
        
        # Read from source and write to destination
        data = self.read_tensor(tensor_in)
        self.write_tensor(data, tensor_out)

    def operation_transpose(self, tensor_in: TensorDescriptor, tensor_out: TensorDescriptor):
        '''Transpose tensor - swap the last two dimensions (dim1 and dim0)'''
        # Input shape: (dim2, dim1, dim0) -> Output shape: (dim2, dim0, dim1)
        expected_shape = (tensor_in.dim2, tensor_in.dim0, tensor_in.dim1)
        
        error_list = [
            (not self._is_in_lmem(tensor_in), 
             f"Source tensor must be in local memory (SPAD/CIMC)"),
            (not self._is_in_lmem(tensor_out), 
             f"Destination tensor must be in local memory (SPAD/CIMC)"),
            (tensor_in.dtype != tensor_out.dtype,
             f"Dtype mismatch: source {tensor_in.dtype} vs destination {tensor_out.dtype}"),
            (tensor_out.shape != expected_shape,
             f"Shape mismatch for transpose: expected {expected_shape}, got {tensor_out.shape}"),
        ]
        
        for error in error_list:
            if error[0]:
                raise ValueError(f"operation_transpose invalid: {error[1]}")
        
        # Read tensor from source
        data = self.read_tensor(tensor_in)
        
        # Transpose last two dimensions: (dim2, dim1, dim0) -> (dim2, dim0, dim1)
        transposed_data = data.transpose(1, 2)
        
        # Write transposed tensor to destination
        self.write_tensor(transposed_data, tensor_out)
    
    def operation_transload(self, tensor_gmem: TensorDescriptor, tensor_lmem: TensorDescriptor):
        '''Load tensor from global memory and transpose to local memory
        Combines load + transpose operations
        Input shape: (dim2, dim1, dim0) -> Output shape: (dim2, dim0, dim1)
        '''
        # Expected output shape after transpose
        expected_shape = (tensor_gmem.dim2, tensor_gmem.dim0, tensor_gmem.dim1)
        
        error_list = [
            (not self._is_in_gmem(tensor_gmem), 
             f"Source tensor must be in global memory (DRAM)"),
            (not self._is_in_lmem(tensor_lmem), 
             f"Destination tensor must be in local memory (SPAD/CIMC)"),
            (tensor_gmem.dtype != tensor_lmem.dtype,
             f"Dtype mismatch: source {tensor_gmem.dtype} vs destination {tensor_lmem.dtype}"),
            (tensor_lmem.shape != expected_shape,
             f"Shape mismatch for transload: expected {expected_shape} after transpose, got {tensor_lmem.shape}"),
        ]
        
        for error in error_list:
            if error[0]:
                raise ValueError(f"operation_transload invalid: {error[1]}")
        
        # Read tensor from global memory
        data = self.read_tensor(tensor_gmem)
        
        # Transpose last two dimensions: (dim2, dim1, dim0) -> (dim2, dim0, dim1)
        transposed_data = data.transpose(1, 2)
        
        # Write transposed tensor to local memory
        self.write_tensor(transposed_data, tensor_lmem)
            
    def operation_gemm(self, 
                      tensor_in: TensorDescriptor,
                      tensor_wt: TensorDescriptor,
                      tensor_orig: TensorDescriptor,
                      tensor_out: TensorDescriptor,
                      accumulate: bool = True):
        '''
        Perform GEMM operation: 
        - If accumulate=True: tensor_out = tensor_in @ tensor_wt + tensor_orig
        - If accumulate=False: tensor_out = tensor_in @ tensor_wt
        Matrix multiplication between last two dimensions
        Only supports batch size of 1 (dim2 == 1)
        Supports mixed precision: converts inputs to FP32 for computation, then to output precision
        '''
        # Expected output shape
        expected_shape = (tensor_in.dim2, tensor_in.dim1, tensor_wt.dim0)
        
        error_list = [
            # Memory location checks
            (not self._is_in_lmem(tensor_in), 
             f"Input tensor must be in local memory (SPAD/CIMC)"),
            (not self._is_in_lmem(tensor_wt), 
             f"Weight tensor must be in local memory (SPAD/CIMC)"),
            (accumulate and not self._is_in_lmem(tensor_orig), 
             f"Original tensor must be in local memory (SPAD/CIMC) when accumulate=True"),
            (not self._is_in_lmem(tensor_out), 
             f"Output tensor must be in local memory (SPAD/CIMC)"),
            
            # Batch size checks (must be 1)
            (tensor_in.dim2 != 1,
             f"Only batch size 1 is supported, tensor_in has batch size {tensor_in.dim2}"),
            (tensor_wt.dim2 != 1,
             f"Only batch size 1 is supported, tensor_wt has batch size {tensor_wt.dim2}"),
            (accumulate and tensor_orig.dim2 != 1,
             f"Only batch size 1 is supported, tensor_orig has batch size {tensor_orig.dim2}"),
            (tensor_out.dim2 != 1,
             f"Only batch size 1 is supported, tensor_out has batch size {tensor_out.dim2}"),
            
            # Data type check - only tensor_orig and tensor_out need to match
            (accumulate and tensor_orig.dtype != tensor_out.dtype,
             f"Dtype mismatch: tensor_orig ({tensor_orig.dtype}) must match tensor_out ({tensor_out.dtype})"),
            
            # Matrix multiplication compatibility
            (tensor_in.dim0 != tensor_wt.dim1,
             f"Matrix multiplication incompatible: tensor_in.dim0({tensor_in.dim0}) != tensor_wt.dim1({tensor_wt.dim1})"),
            
            # Output shape check
            (tensor_out.shape != expected_shape,
             f"Output shape mismatch: expected {expected_shape}, got {tensor_out.shape}"),
            
            # tensor_orig shape check
            (accumulate and tensor_orig.shape != tensor_out.shape,
             f"Original tensor shape {tensor_orig.shape} doesn't match output shape {tensor_out.shape}"),
        ]
        
        for error in error_list:
            if error[0]:
                raise ValueError(f"operation_gemm invalid: {error[1]}")
        
        # Read tensors from memory
        data_in = self.read_tensor(tensor_in)
        data_wt = self.read_tensor(tensor_wt)
        
        # Convert inputs to FP32 for computation
        data_in_fp32 = data_in.to(torch.float32)
        data_wt_fp32 = data_wt.to(torch.float32)
        
        # Perform matrix multiplication in FP32: (batch, M, K) @ (batch, K, N) -> (batch, M, N)
        data_out_fp32 = torch.bmm(data_in_fp32, data_wt_fp32)
        
        # Add original tensor if accumulate is True
        if accumulate:
            data_orig = self.read_tensor(tensor_orig)
            data_orig_fp32 = data_orig.to(torch.float32)
            data_out_fp32 = data_out_fp32 + data_orig_fp32
        
        # Convert result to output dtype
        _, out_torch_dtype = dtype_convert(tensor_out.dtype)
        if tensor_out.dtype == "INT4":
            # For INT4, clamp to valid range and convert to int8
            data_out = torch.clamp(data_out_fp32, -8, 7).round().to(torch.int8)
        elif tensor_out.dtype in ["INT8", "INT16", "INT32"]:
            # For integer types, round and convert
            data_out = data_out_fp32.round().to(out_torch_dtype)
        else:
            # For floating point types, direct conversion
            data_out = data_out_fp32.to(out_torch_dtype)
        
        # Write result to output
        self.write_tensor(data_out, tensor_out)

    @staticmethod
    def arithmetic_op(op: str):
        _map = {
            "add" : torch.add,
            "sub" : torch.sub,
            "max" : torch.maximum,  # element-wise maximum
            "min" : torch.minimum,  # element-wise minimum
            "mul" : torch.mul,
            "eq"  : torch.eq,       
            "ne"  : torch.ne,
            "gt"  : torch.greater,
            "lt"  : torch.less,
            "ge"  : torch.greater_equal,
            "le"  : torch.less_equal,
        }

        if op not in _map:
            raise ValueError(f"Unsupported arithmetic operation: {op}")
        return _map[op]

    def operation_vvv(self, 
                     tensor_in1: TensorDescriptor,
                     tensor_in2: TensorDescriptor,
                     tensor_out: TensorDescriptor,
                     op: str):
        '''
        Perform element-wise operation: tensor_out = op_func(tensor_in1, tensor_in2)
        Supports mixed precision: converts inputs to FP32 for computation, then to output precision
        '''
        error_list = [
            # Memory location checks
            (not self._is_in_lmem(tensor_in1), 
             f"Input tensor 1 must be in local memory (SPAD/CIMC)"),
            (not self._is_in_lmem(tensor_in2), 
             f"Input tensor 2 must be in local memory (SPAD/CIMC)"),
            (not self._is_in_lmem(tensor_out), 
             f"Output tensor must be in local memory (SPAD/CIMC)"),
            
            # Dimension checks - dim2 and dim1 must be 1
            (tensor_in1.dim2 != 1,
             f"Only dim2=1 is supported, tensor_in1 has dim2={tensor_in1.dim2}"),
            (tensor_in1.dim1 != 1,
             f"Only dim1=1 is supported, tensor_in1 has dim1={tensor_in1.dim1}"),
            (tensor_in2.dim2 != 1,
             f"Only dim2=1 is supported, tensor_in2 has dim2={tensor_in2.dim2}"),
            (tensor_in2.dim1 != 1,
             f"Only dim1=1 is supported, tensor_in2 has dim1={tensor_in2.dim1}"),
            (tensor_out.dim2 != 1,
             f"Only dim2=1 is supported, tensor_out has dim2={tensor_out.dim2}"),
            (tensor_out.dim1 != 1,
             f"Only dim1=1 is supported, tensor_out has dim1={tensor_out.dim1}"),
            
            # Shape checks - all must have the same shape
            (tensor_in1.shape != tensor_in2.shape,
             f"Shape mismatch: tensor_in1 {tensor_in1.shape} vs tensor_in2 {tensor_in2.shape}"),
            (tensor_in1.shape != tensor_out.shape,
             f"Shape mismatch: input {tensor_in1.shape} vs output {tensor_out.shape}"),
            
            # Operation validity check
            (op not in ["add", "sub", "max", "min", "mul", "eq", "ne", "gt", "lt", "ge", "le"],
             f"Unsupported operation: {op}"),
        ]
        
        for error in error_list:
            if error[0]:
                raise ValueError(f"operation_vvv invalid: {error[1]}")
        
        # Read tensors from memory
        data_in1 = self.read_tensor(tensor_in1)
        data_in2 = self.read_tensor(tensor_in2)
        
        # Convert inputs to FP32 for computation
        data_in1_fp32 = data_in1.to(torch.float32)
        data_in2_fp32 = data_in2.to(torch.float32)
        
        # Perform operation
        op_func = self.arithmetic_op(op)
        data_out_fp32 = op_func(data_in1_fp32, data_in2_fp32)
        
        # For comparison operations, the result is boolean, convert to float
        if op in ["eq", "ne", "gt", "lt", "ge", "le"]:
            data_out_fp32 = data_out_fp32.to(torch.float32)
        
        # Convert result to output dtype
        _, out_torch_dtype = dtype_convert(tensor_out.dtype)
        if tensor_out.dtype == "INT4":
            # For INT4, clamp to valid range and convert to int8
            data_out = torch.clamp(data_out_fp32, -8, 7).round().to(torch.int8)
        elif tensor_out.dtype in ["INT8", "INT16", "INT32"]:
            # For integer types, round and convert
            data_out = data_out_fp32.round().to(out_torch_dtype)
        else:
            # For floating point types, direct conversion
            data_out = data_out_fp32.to(out_torch_dtype)
        
        # Write result to output
        self.write_tensor(data_out, tensor_out)    

    def operation_vsv(self,
                     tensor_in: TensorDescriptor,
                     tensor_out: TensorDescriptor,
                     scalar_in: int,
                     dtype_scalar: str,
                     op: str):
        '''
        Perform element-wise operation: tensor_out = op_func(tensor_in, scalar_in)
        Supports mixed precision: converts inputs to FP32 for computation, then to output precision
        scalar_in is passed as int but interpreted based on dtype_scalar
        '''
        error_list = [
            # Memory location checks
            (not self._is_in_lmem(tensor_in), 
             f"Input tensor must be in local memory (SPAD/CIMC)"),
            (not self._is_in_lmem(tensor_out), 
             f"Output tensor must be in local memory (SPAD/CIMC)"),
            
            # Dimension checks - dim2 and dim1 must be 1
            (tensor_in.dim2 != 1,
             f"Only dim2=1 is supported, tensor_in has dim2={tensor_in.dim2}"),
            (tensor_in.dim1 != 1,
             f"Only dim1=1 is supported, tensor_in has dim1={tensor_in.dim1}"),
            (tensor_out.dim2 != 1,
             f"Only dim2=1 is supported, tensor_out has dim2={tensor_out.dim2}"),
            (tensor_out.dim1 != 1,
             f"Only dim1=1 is supported, tensor_out has dim1={tensor_out.dim1}"),
            
            # Shape checks - input and output must have the same shape
            (tensor_in.shape != tensor_out.shape,
             f"Shape mismatch: input {tensor_in.shape} vs output {tensor_out.shape}"),
            
            # Operation validity check
            (op not in ["add", "sub", "mul", "min", "max", "eq", "ne", "gt", "lt", "ge", "le"],
             f"Unsupported operation: {op}"),
            
            # Scalar dtype validity check
            (dtype_scalar not in ["INT4", "INT8", "INT16", "INT32", "FP16", "FP32", "BF16"],
             f"Unsupported scalar dtype: {dtype_scalar}"),
        ]
        
        # Check all errors
        error_msgs = [msg for cond, msg in error_list if cond]
        if error_msgs:
            raise ValueError(f"operation_vsv invalid: {'; '.join(error_msgs)}")
        
        # Read input tensor
        data_in = self.read_tensor(tensor_in)
        
        # Convert scalar_in from int representation to actual value
        scalar_value = scalar_from_int(scalar_in, dtype_scalar)
        
        # Convert inputs to FP32 for computation
        data_in_fp32 = data_in.to(torch.float32)
        scalar_fp32 = float(scalar_value)

        
        # Perform operation
        if op in ["eq", "ne", "gt", "lt", "ge", "le"]:
            # Comparison operations
            op_func = self.arithmetic_op(op)
            data_out_fp32 = op_func(data_in_fp32, scalar_fp32).to(torch.float32)
        elif op in ["min", "max"]:
            # Min/max need scalar as tensor
            scalar_tensor = torch.tensor(scalar_fp32, dtype=torch.float32)
            op_func = self.arithmetic_op(op)
            data_out_fp32 = op_func(data_in_fp32, scalar_tensor)
        else:
            # Arithmetic operations (add, sub, mul)
            op_func = self.arithmetic_op(op)
            data_out_fp32 = op_func(data_in_fp32, scalar_fp32)
        
        # Convert result to output dtype
        _, out_torch_dtype = dtype_convert(tensor_out.dtype)
        if tensor_out.dtype == "INT4":
            # For INT4, clamp to valid range and convert to int8
            data_out = torch.clamp(data_out_fp32, -8, 7).round().to(torch.int8)
        elif tensor_out.dtype in ["INT8", "INT16", "INT32"]:
            # For integer types, round and convert
            data_out = data_out_fp32.round().to(out_torch_dtype)
        else:
            # For floating point types, direct conversion
            data_out = data_out_fp32.to(out_torch_dtype)
        
        # Write result to output
        self.write_tensor(data_out, tensor_out)
    
    def operation_vs(self,
                    tensor_in: TensorDescriptor,
                    op: str) -> int:
        '''
        Perform reduce operation: scalar_out = reduce(op, tensor_in)
        Supported ops: "add" (sum), "gt" (max), "lt" (min)
        Returns result as int - for FP types, returns IEEE754 FP32 bit representation
        For INT types, returns the integer value directly
        '''
        error_list = [
            # Memory location checks
            (not self._is_in_lmem(tensor_in), 
             f"Input tensor must be in local memory (SPAD/CIMC)"),
            
            # Dimension checks - dim2 and dim1 must be 1
            (tensor_in.dim2 != 1,
             f"Only dim2=1 is supported, tensor_in has dim2={tensor_in.dim2}"),
            (tensor_in.dim1 != 1,
             f"Only dim1=1 is supported, tensor_in has dim1={tensor_in.dim1}"),
            
            # Operation validity check
            (op not in ["add", "gt", "lt"],
             f"Unsupported reduce operation: {op}. Supported: add (sum), gt (max), lt (min)"),
        ]
        
        # Check all errors
        error_msgs = [msg for cond, msg in error_list if cond]
        if error_msgs:
            raise ValueError(f"operation_vs invalid: {'; '.join(error_msgs)}")
        
        # Read input tensor
        data_in = self.read_tensor(tensor_in)
        
        # Convert to FP32 for computation
        data_in_fp32 = data_in.to(torch.float32).flatten()
        
        # Perform reduce operation
        if op == "add":
            # Sum all elements
            result_fp32 = torch.sum(data_in_fp32).item()
        elif op == "gt":
            # Find maximum using gt (greater than)
            result_fp32 = torch.max(data_in_fp32).item()
        elif op == "lt":
            # Find minimum using lt (less than)
            result_fp32 = torch.min(data_in_fp32).item()
        
        # Convert result to output format
        if tensor_in.dtype in ["FP16", "FP32", "BF16"]:
            # For floating point types, return IEEE754 FP32 bit representation
            result_int = torch.tensor(result_fp32, dtype=torch.float32).view(torch.uint32).item()
        else:
            # For integer types, return the integer value directly
            result_int = int(result_fp32)
        
        return result_int
    
    def operation_vv(self,
                    tensor_in: TensorDescriptor,
                    tensor_out: TensorDescriptor):
        '''
        Perform type conversion: tensor_out = type_cast(tensor_in)
        Reads tensor from LMEM, converts type, and writes back to LMEM
        '''
        error_list = [
            # Memory location checks
            (not self._is_in_lmem(tensor_in), 
             f"Input tensor must be in local memory (SPAD/CIMC)"),
            (not self._is_in_lmem(tensor_out), 
             f"Output tensor must be in local memory (SPAD/CIMC)"),
            
            # Dimension checks - dim2 and dim1 must be 1
            (tensor_in.dim2 != 1,
             f"Only dim2=1 is supported, tensor_in has dim2={tensor_in.dim2}"),
            (tensor_in.dim1 != 1,
             f"Only dim1=1 is supported, tensor_in has dim1={tensor_in.dim1}"),
            (tensor_out.dim2 != 1,
             f"Only dim2=1 is supported, tensor_out has dim2={tensor_out.dim2}"),
            (tensor_out.dim1 != 1,
             f"Only dim1=1 is supported, tensor_out has dim1={tensor_out.dim1}"),
            
            # Shape checks - input and output must have the same shape
            (tensor_in.shape != tensor_out.shape,
             f"Shape mismatch: input {tensor_in.shape} vs output {tensor_out.shape}"),
        ]
        
        # Check all errors
        error_msgs = [msg for cond, msg in error_list if cond]
        if error_msgs:
            raise ValueError(f"operation_vv invalid: {'; '.join(error_msgs)}")
        
        # Read input tensor
        data_in = self.read_tensor(tensor_in)
        
        # Convert type
        _, out_torch_dtype = dtype_convert(tensor_out.dtype)
        
        if tensor_out.dtype == "INT4":
            # For INT4, clamp to valid range and convert to int8
            if tensor_in.dtype in ["FP16", "FP32", "BF16"]:
                # From floating point: round first
                data_out = torch.clamp(data_in.round(), -8, 7).to(torch.int8)
            else:
                # From integer: just clamp
                data_out = torch.clamp(data_in, -8, 7).to(torch.int8)
        elif tensor_out.dtype in ["INT8", "INT16", "INT32"]:
            # For integer types
            if tensor_in.dtype in ["FP16", "FP32", "BF16"]:
                # From floating point: round first
                data_out = data_in.round().to(out_torch_dtype)
            else:
                # From integer: direct conversion
                data_out = data_in.to(out_torch_dtype)
        else:
            # For floating point types, direct conversion
            data_out = data_in.to(out_torch_dtype)
        
        # Write result to output
        self.write_tensor(data_out, tensor_out)


class GOLDEN_VM_SYSTEM:
    '''System with multiple GOLDEN_VM_CORE instances'''
    def __init__(self):
        self.cores = []
        for i in range(CONFIG.NUM_CORES):
            self.cores.append(GOLDEN_VM_CORE())
        print(f"GOLDEN_VM_SYSTEM initialized with {CONFIG.NUM_CORES} cores")

        # Simple logging system
        self.log_entries = []  # Store log entries

        # NOC transfer buffer for inter-core communication
        self.noc_transfer_buffer = {}
    
    def _log(self, func_name: str, **kwargs):
        '''Simple logging function - stores log entries'''
        # Format parameters
        params = []
        for key, value in kwargs.items():
            if isinstance(value, TensorDescriptor):
                params.append(f"{key}=TensorDescriptor(addr=0x{value.addr:08X}, shape={value.shape}, byte_stride={value.byte_stride}, dtype={value.dtype})")
            elif isinstance(value, torch.Tensor):
                params.append(f"{key}=Tensor(shape={list(value.shape)}, dtype={value.dtype})")
            else:
                params.append(f"{key}={value}")
        
        # Store log entry
        log_entry = f"[GOLDEN_VM_SYSTEM] {func_name}({', '.join(params)})"
        self.log_entries.append(log_entry)
    
    def save_log(self, log_file_path: str):
        '''Save log entries to file'''
        with open(log_file_path, 'w') as f:
            for entry in self.log_entries:
                f.write(entry + '\n')
        print(f"Log saved to {log_file_path}")
    
    def _check_core_id(self, core_id: int):
        '''Check if core_id is valid'''
        if not (0 <= core_id < CONFIG.NUM_CORES):
            raise ValueError(f"Invalid core_id {core_id}. Must be in range [0, {CONFIG.NUM_CORES})")
    
    def read_tensor(self, core_id: int, tensor: TensorDescriptor) -> torch.Tensor:
        '''Read tensor using specified core'''
        self._check_core_id(core_id)
        self._log("read_tensor", core_id=core_id, tensor=tensor)
        return self.cores[core_id].read_tensor(tensor)
    
    def write_tensor(self, core_id: int, tensor_in: torch.Tensor, tensor_desc: TensorDescriptor):
        '''Write tensor using specified core'''
        self._check_core_id(core_id)
        self._log("write_tensor", core_id=core_id, tensor_in=tensor_in, tensor_desc=tensor_desc)
        self.cores[core_id].write_tensor(tensor_in, tensor_desc)
    
    def operation_load(self, core_id: int, tensor_gmem: TensorDescriptor, tensor_lmem: TensorDescriptor):
        '''Load tensor from global to local memory using specified core'''
        self._check_core_id(core_id)
        self._log("operation_load", core_id=core_id, tensor_gmem=tensor_gmem, tensor_lmem=tensor_lmem)
        self.cores[core_id].operation_load(tensor_gmem, tensor_lmem)
    
    def operation_store(self, core_id: int, tensor_lmem: TensorDescriptor, tensor_gmem: TensorDescriptor):
        '''Store tensor from local to global memory using specified core'''
        self._check_core_id(core_id)
        self._log("operation_store", core_id=core_id, tensor_lmem=tensor_lmem, tensor_gmem=tensor_gmem)
        self.cores[core_id].operation_store(tensor_lmem, tensor_gmem)
    
    def operation_move(self, core_id: int, tensor_src: TensorDescriptor, tensor_dst: TensorDescriptor):
        '''Move tensor between local memories using specified core'''
        self._check_core_id(core_id)
        self._log("operation_move", core_id=core_id, tensor_src=tensor_src, tensor_dst=tensor_dst)
        self.cores[core_id].operation_move(tensor_src, tensor_dst)
    
    def operation_transpose(self, core_id: int, tensor_in: TensorDescriptor, tensor_out: TensorDescriptor):
        '''Transpose tensor using specified core'''
        self._check_core_id(core_id)
        self._log("operation_transpose", core_id=core_id, tensor_in=tensor_in, tensor_out=tensor_out)
        self.cores[core_id].operation_transpose(tensor_in, tensor_out)
    
    def operation_transload(self, core_id: int, tensor_gmem: TensorDescriptor, tensor_lmem: TensorDescriptor):
        '''Load tensor from global memory and transpose to local memory using specified core'''
        self._check_core_id(core_id)
        self._log("operation_transload", core_id=core_id, tensor_gmem=tensor_gmem, tensor_lmem=tensor_lmem)
        self.cores[core_id].operation_transload(tensor_gmem, tensor_lmem)
    
    def operation_gemm(self, core_id: int, 
                      tensor_in: TensorDescriptor,
                      tensor_wt: TensorDescriptor,
                      tensor_orig: TensorDescriptor,
                      tensor_out: TensorDescriptor,
                      accumulate: bool = True):
        '''Perform GEMM operation using specified core'''
        self._check_core_id(core_id)
        self._log("operation_gemm", core_id=core_id, tensor_in=tensor_in, tensor_wt=tensor_wt, 
                  tensor_orig=tensor_orig, tensor_out=tensor_out, accumulate=accumulate)
        self.cores[core_id].operation_gemm(tensor_in, tensor_wt, tensor_orig, tensor_out, accumulate)
    
    def operation_vvv(self, core_id: int,
                     tensor_in1: TensorDescriptor,
                     tensor_in2: TensorDescriptor,
                     tensor_out: TensorDescriptor,
                     op: str = 'add'):
        '''Perform element-wise vector operation using specified core'''
        self._check_core_id(core_id)
        self._log("operation_vvv", core_id=core_id, tensor_in1=tensor_in1, tensor_in2=tensor_in2, 
                  tensor_out=tensor_out, op=op)
        self.cores[core_id].operation_vvv(tensor_in1, tensor_in2, tensor_out, op)
    
    def operation_vsv(self, core_id: int,
                     tensor_in: TensorDescriptor,
                     tensor_out: TensorDescriptor,
                     scalar_in: int,
                     dtype_scalar: str,
                     op: str = 'add'):
        '''Perform vector-scalar operation using specified core'''
        self._check_core_id(core_id)
        self._log("operation_vsv", core_id=core_id, tensor_in=tensor_in, tensor_out=tensor_out,
                  scalar_in=scalar_in, dtype_scalar=dtype_scalar, op=op)
        self.cores[core_id].operation_vsv(tensor_in, tensor_out, scalar_in, dtype_scalar, op)
    
    def operation_vs(self, core_id: int,
                    tensor_in: TensorDescriptor,
                    op: str = 'add') -> int:
        '''Perform vector reduce operation using specified core'''
        self._check_core_id(core_id)
        self._log("operation_vs", core_id=core_id, tensor_in=tensor_in, op=op)
        return self.cores[core_id].operation_vs(tensor_in, op)
    
    def operation_vv(self, core_id: int,
                    tensor_in: TensorDescriptor,
                    tensor_out: TensorDescriptor):
        '''Perform vector type conversion using specified core'''
        self._check_core_id(core_id)
        self._log("operation_vv", core_id=core_id, tensor_in=tensor_in, tensor_out=tensor_out)
        self.cores[core_id].operation_vv(tensor_in, tensor_out)
    
    def operation_noc(self,
                      tensor_src: TensorDescriptor, src_core_id: int,
                      tensor_dst: TensorDescriptor, dst_core_id: int
                      ):
        raise NotImplementedError()
    
    def get_core(self, core_id: int) -> GOLDEN_VM_CORE:
        '''Get a specific core instance'''
        self._check_core_id(core_id)
        return self.cores[core_id]
    
    def get_all_cores(self) -> list:
        '''Get all core instances'''
        return self.cores
    
    def data_import(self, json_file_path: str, data_dir: str, use_pt_format: bool = True):
        '''
        Import tensor data from JSON configuration and PT/NPY files
        
        Args:
            json_file_path: Path to JSON file containing tensor configurations
            data_dir: Directory containing PT/NPY files with tensor data
            use_pt_format: If True, use .pt format; if False, use .npy format
        '''
        import json
        import numpy as np
        import os
        
        # Create directory if it doesn't exist
        if not os.path.exists(data_dir):
            os.makedirs(data_dir)
        
        # Load tensor configurations from JSON
        with open(json_file_path, 'r') as file:
            tensor_configs = json.load(file)
        
        print(f"\nImporting {len(tensor_configs)} tensors from {json_file_path}")
        print("=" * 60)
        
        self._log("data_import", json_file_path=json_file_path, data_dir=data_dir, use_pt_format=use_pt_format)
        
        for tensor_config in tensor_configs:
            # Parse tensor configuration
            tensor_name = tensor_config['name']
            tensor_addr = int(tensor_config['address'], 16)
            
            # Handle both 'shape' and 'dimension' keys for compatibility
            if 'shape' in tensor_config:
                shape_key = 'shape'
            elif 'dimension' in tensor_config:
                shape_key = 'dimension'
            else:
                raise KeyError(f"Tensor {tensor_name} has neither 'shape' nor 'dimension' key")
                
            tensor_shape = (
                tensor_config[shape_key]['dim2'],
                tensor_config[shape_key]['dim1'], 
                tensor_config[shape_key]['dim0']
            )
            tensor_stride = (
                tensor_config['stride']['stride2'] * CONFIG.WORD_BYTES,  # Convert word stride to byte stride
                tensor_config['stride']['stride1'] * CONFIG.WORD_BYTES,  # Convert word stride to byte stride
                tensor_config['stride']['stride0b'] * CONFIG.WORD_BYTES   # Convert word stride to byte stride
            )
            tensor_dtype = tensor_config['datatype']
            
            # Parse core assignment from tensor name (format: "tensor_coreID_index")
            name_parts = tensor_name.split('_')
            core_id = int(name_parts[1]) * 4 + int(name_parts[2])
                
            # Validate core_id
            if core_id >= CONFIG.NUM_CORES:
                print(f"Warning: core_id {core_id} exceeds available cores. Using core 0.")
                core_id = 0
            
            # Load tensor data from PT or NPY file
            if use_pt_format:
                file_path = os.path.join(data_dir, f"{tensor_name}.pt")
                if not os.path.exists(file_path):
                    print(f"Warning: PT file not found: {file_path}")
                    continue
                # Load PyTorch tensor directly
                tensor_torch = torch.load(file_path)
            else:
                file_path = os.path.join(data_dir, f"{tensor_name}.npy")
                if not os.path.exists(file_path):
                    print(f"Warning: NPY file not found: {file_path}")
                    continue
                tensor_data = np.load(file_path)
            
            # Convert to PyTorch tensor if needed
            if not use_pt_format:
                # Convert numpy array to PyTorch tensor with appropriate dtype
                width, torch_dtype = dtype_convert(tensor_dtype)
                if tensor_dtype == "INT4":
                    # INT4 uses int8 storage
                    tensor_torch = torch.from_numpy(tensor_data).to(torch.int8)
                else:
                    tensor_torch = torch.from_numpy(tensor_data).to(torch_dtype)
            else:
                # For PT format, ensure correct dtype
                width, torch_dtype = dtype_convert(tensor_dtype)
                if tensor_dtype == "INT4":
                    tensor_torch = tensor_torch.to(torch.int8)
                else:
                    tensor_torch = tensor_torch.to(torch_dtype)
            
            # Reshape if necessary
            if tensor_torch.shape != tensor_shape:
                try:
                    tensor_torch = tensor_torch.reshape(tensor_shape)
                except Exception as e:
                    print(f"Error: Cannot reshape {tensor_name} from {tensor_torch.shape} to {tensor_shape}: {e}")
                    continue
            
            # Create tensor descriptor
            tensor_desc = TensorDescriptor(tensor_addr, tensor_shape, tensor_stride, tensor_dtype)
            
            # Find which memory contains this address
            core = self.cores[core_id]
            mem_name = "Unknown"
            for mem in core.lmem_list + core.dram_list:
                if mem.contain_tensor(tensor_desc):
                    mem_name = mem.name
                    break
            
            print(f"\nImporting tensor: {tensor_name}")
            print(f"  Core ID: {core_id}")
            print(f"  Memory: {mem_name}")
            print(f"  Address: 0x{tensor_addr:08x}")
            print(f"  Shape: {tensor_shape}")
            print(f"  Dtype: {tensor_dtype}")
            print(f"  Data range: [{tensor_torch.min().item():.4f}, {tensor_torch.max().item():.4f}]")
            
            # Write tensor to memory
            try:
                self.write_tensor(core_id, tensor_torch, tensor_desc)
                print(f"  ✓ Successfully imported")
            except Exception as e:
                print(f"  ✗ Error importing: {e}")
        
        print("\n" + "=" * 60)
        print("Data import completed")
    
    def data_export(self, json_file_path: str, data_dir: str, use_pt_format: bool = True):
        '''
        Export tensor data to PT/NPY files based on JSON configuration
        
        Args:
            json_file_path: Path to JSON file containing tensor configurations
            data_dir: Directory to save PT/NPY files with tensor data
            use_pt_format: If True, use .pt format; if False, use .npy format
        '''
        import json
        import numpy as np
        import os
        
        # Create directory if it doesn't exist
        if not os.path.exists(data_dir):
            os.makedirs(data_dir)
        
        # Load tensor configurations from JSON
        with open(json_file_path, 'r') as file:
            tensor_configs = json.load(file)
        
        print(f"\nExporting {len(tensor_configs)} tensors to {data_dir}")
        print("=" * 60)
        
        self._log("data_export", json_file_path=json_file_path, data_dir=data_dir, use_pt_format=use_pt_format)
        
        for tensor_config in tensor_configs:
            # Parse tensor configuration
            tensor_name = tensor_config['name']
            tensor_addr = int(tensor_config['address'], 16)
            
            # Handle both 'shape' and 'dimension' keys for compatibility
            if 'shape' in tensor_config:
                shape_key = 'shape'
            elif 'dimension' in tensor_config:
                shape_key = 'dimension'
            else:
                raise KeyError(f"Tensor {tensor_name} has neither 'shape' nor 'dimension' key")
                
            tensor_shape = (
                tensor_config[shape_key]['dim2'],
                tensor_config[shape_key]['dim1'], 
                tensor_config[shape_key]['dim0']
            )
            tensor_stride = (
                tensor_config['stride']['stride2'] * CONFIG.WORD_BYTES,
                tensor_config['stride']['stride1'] * CONFIG.WORD_BYTES,
                tensor_config['stride']['stride0b'] * CONFIG.WORD_BYTES
            )
            tensor_dtype = tensor_config['datatype']
            
            # Parse core assignment
            name_parts = tensor_name.split('_')
            core_id = int(name_parts[1]) * 4 + int(name_parts[2])
            
                
            if core_id >= CONFIG.NUM_CORES:
                core_id = 0
            
            # Create tensor descriptor
            tensor_desc = TensorDescriptor(tensor_addr, tensor_shape, tensor_stride, tensor_dtype)
            
            print(f"\nExporting tensor: {tensor_name}")
            print(f"  Core ID: {core_id}")
            print(f"  Address: 0x{tensor_addr:08x}")
            print(f"  Shape: {tensor_shape}")
            print(f"  Dtype: {tensor_dtype}")
            
            try:
                # Read tensor from memory
                tensor_torch = self.read_tensor(core_id, tensor_desc)
                
                # Save to PT or NPY file
                if use_pt_format:
                    # Save as PyTorch tensor
                    file_path = os.path.join(data_dir, f"{tensor_name}.pt")
                    torch.save(tensor_torch.detach().cpu(), file_path)
                    print(f"  Data range: [{tensor_torch.min().item():.4f}, {tensor_torch.max().item():.4f}]")
                else:
                    # Convert to numpy array
                    if tensor_dtype == "BF16":
                        # BFloat16 needs special handling - convert to float32 for numpy
                        tensor_numpy = tensor_torch.to(torch.float32).detach().cpu().numpy()
                    else:
                        tensor_numpy = tensor_torch.detach().cpu().numpy()
                    
                    # Save to NPY file
                    file_path = os.path.join(data_dir, f"{tensor_name}.npy")
                    np.save(file_path, tensor_numpy)
                    print(f"  Data range: [{tensor_numpy.min():.4f}, {tensor_numpy.max():.4f}]")
                
                print(f"  ✓ Successfully exported to {file_path}")
                
            except Exception as e:
                print(f"  ✗ Error exporting: {e}")
        
        print("\n" + "=" * 60)
        print("Data export completed")


if __name__ == "__main__":
    import json
    import os
    
    # Example: Matrix multiplication reading from input_tensor.json
    print("=" * 80)
    print("GOLDEN_VM Matrix Multiplication from input_tensor.json")
    print("=" * 80)
    
    # Create GOLDEN_VM_SYSTEM instance for data import
    system = GOLDEN_VM_SYSTEM()
    
    # Input and output configuration
    json_file = "input_tensor.json"
    input_dir = "in_data"
    output_dir = "out_data"
    
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Read tensor configuration from JSON
    print(f"\n1. Reading tensor configuration from {json_file}...")
    with open(json_file, 'r') as f:
        tensor_configs = json.load(f)
    
    # Import tensors from .pt files
    print(f"\n2. Importing tensors from {input_dir}...")
    system.data_import(json_file, input_dir, use_pt_format=True)
    
    # Find input tensor configurations
    in1_config = next(t for t in tensor_configs if t['name'] == 'in1_0_0')
    wt_config = next(t for t in tensor_configs if t['name'] == 'wt_0_0')
    orig_config = next(t for t in tensor_configs if t['name'] == 'orig_0_0')
    
    # Read output tensor configuration from output_tensor.json
    output_json_file = "output_tensor.json"
    print(f"\n2.5. Reading output tensor configuration from {output_json_file}...")
    with open(output_json_file, 'r') as f:
        output_configs = json.load(f)
    output_config = output_configs[0]  # Use the first output tensor
    
    # Create tensor descriptors
    def create_descriptor(config):
        addr = int(config['address'], 16)
        shape = (
            config['dimension']['dim2'],
            config['dimension']['dim1'],
            config['dimension']['dim0']
        )
        stride = (
            config['stride']['stride2'] * CONFIG.WORD_BYTES,
            config['stride']['stride1'] * CONFIG.WORD_BYTES,
            config['stride']['stride0b'] * CONFIG.WORD_BYTES
        )
        dtype = config['datatype']
        return TensorDescriptor(addr, shape, stride, dtype)
    
    desc_in1 = create_descriptor(in1_config)
    desc_wt = create_descriptor(wt_config)
    desc_orig = create_descriptor(orig_config)
    desc_out = create_descriptor(output_config)
    
    # Perform GEMM operation on core 0
    core_id = 0
    print(f"\n3. Performing GEMM operation on core {core_id}...")
    print(f"   out = in1_0_0 @ wt_0_0 + orig_0_0")
    print(f"   Shapes: ({desc_in1.shape[1]},{desc_in1.shape[2]}) @ ({desc_wt.shape[1]},{desc_wt.shape[2]}) + ({desc_orig.shape[1]},{desc_orig.shape[2]})")
    
    system.operation_gemm(core_id, desc_in1, desc_wt, desc_orig, desc_out)
    print("   ✓ GEMM operation completed")
    
    # Export result using data_export
    print(f"\n4. Exporting output tensor to {output_dir}...")
    system.data_export(output_json_file, output_dir, use_pt_format=True)
    
    # Read the result back to verify and print statistics
    print("\n5. Verifying exported output...")
    result = system.read_tensor(core_id, desc_out)
    print(f"   Result shape: {result.shape}, dtype: {result.dtype}")
    
    # Print result statistics
    print("\n6. Result statistics:")
    print(f"   Output min: {torch.min(result).item():.6f}")
    print(f"   Output max: {torch.max(result).item():.6f}")
    print(f"   Output mean: {torch.mean(result).item():.6f}")
    print(f"   Output std: {torch.std(result).item():.6f}")
    
    # Optionally verify against reference output if available
    ref_file = os.path.join(input_dir, "ref_out_mm.pt")
    if os.path.exists(ref_file):
        print(f"\n7. Verifying against reference output...")
        ref_output = torch.load(ref_file)
        
        if torch.allclose(result, ref_output, rtol=1e-2, atol=1e-3):
            max_error = torch.max(torch.abs(result - ref_output)).item()
            print(f"   ✅ Result matches reference output! max_error: {max_error}")
        else:
            max_error = torch.max(torch.abs(result - ref_output)).item()
            print(f"   ⚠️  Result differs from reference, max error: {max_error}")
    
    # Save execution log
    log_file = os.path.join(output_dir, "execution_log.txt")
    system.save_log(log_file)
    
    print("\n" + "=" * 80)
    print("Matrix multiplication completed successfully!")
    print("=" * 80)
