# Bug Fix: NOC Transmission

## Fix Summary
Fixed the NOC primitive pairing logic to correctly match SRC and DEST primitives based on their execution cores and target specifications.

## Changes Made

### 1. SIMTop.py - Fixed Pairing Logic

#### Added Executing Core Identification (handle_noc_primitive)
```python
# Identify executing core based on group and mask
executing_core_id = None
executing_core = None
for i in range(self.col):
    for j in range(self.row):
        core = self.npu_cores[i][j]
        if core.group == primitive.npu_group and (primitive.npu_mask & (1 << core.id)) != 0:
            executing_core_id = core.id
            executing_core = core
            break
            
# Store executing core info in primitive for matching
primitive.executing_core_id = executing_core_id
primitive.executing_core_group = primitive.npu_group
```

#### Fixed is_matching_pair Function
```python
def is_matching_pair(self, prim1, prim2):
    """Check if two primitives form a valid NoC transfer pair"""
    # Must be different types (one SRC, one DEST)
    if prim1.type == prim2.type:
        return False
    
    # Must be NOC_SRC and NOC_DEST
    if {prim1.type, prim2.type} != {SIMTemp.PrimName.NOC_SRC, SIMTemp.PrimName.NOC_DEST}:
        return False
    
    # Identify which is SRC and which is DEST
    if prim1.type == SIMTemp.PrimName.NOC_SRC:
        src_prim = prim1
        dst_prim = prim2
    else:
        src_prim = prim2
        dst_prim = prim1
    
    # Check matching logic:
    # 1. SRC's destination should match DEST's executing core
    # 2. DEST's source should match SRC's executing core
    src_dst_matches = (src_prim.noc_settings.dst_id == dst_prim.executing_core_id and
                      src_prim.noc_settings.dst_group[0] == dst_prim.executing_core_group)
    
    dst_src_matches = (dst_prim.noc_settings.src_id == src_prim.executing_core_id and
                      dst_prim.noc_settings.src_group[0] == src_prim.executing_core_group)
    
    return src_dst_matches and dst_src_matches
```

#### Enhanced execute_noc_transfer_pair
- Added core resolution logic
- Added debug logging for transfer details
- Added transfer validation support

### 2. SIMTemplate.py - Added Transfer Validation

#### NOC_SRC Validation
```python
# Debug validation
if hasattr(self.noc_settings, 'validate_transfer') and self.noc_settings.validate_transfer:
    print(f"[NoC Validation] SRC {self.prim_id} read {len(data)} bytes from Core {core_id}")
    print(f"                 Transfer ID: {getattr(self.noc_settings, 'transfer_id', 'N/A')}")
    print(f"                 First 10 values: {data[:10] if len(data) > 10 else data}")
```

#### NOC_DEST Validation
```python
# Debug validation
if hasattr(self.noc_settings, 'validate_transfer') and self.noc_settings.validate_transfer:
    print(f"[NoC Validation] DEST {self.prim_id} wrote {len(data)} bytes to Core {core_id}")
    print(f"                 Transfer ID: {getattr(self.noc_settings, 'transfer_id', 'N/A')}")
    print(f"                 First 10 values: {data[:10] if len(data) > 10 else data}")
    print(f"[NoC Validation] Transfer COMPLETE: {getattr(self.noc_settings, 'transfer_id', 'N/A')}")
```

## How the Fix Works

1. **Executing Core Identification**: When a NOC primitive is handled, we identify which core is executing it based on the group and mask information.

2. **Correct Pairing Logic**: Instead of requiring all fields to match, we check:
   - SRC's dst_id matches DEST's executing core
   - DEST's src_id matches SRC's executing core

3. **Debug Validation**: When debug mode is enabled, the system tracks:
   - Data read by SRC primitive
   - Data written by DEST primitive
   - Transfer completion status

## Testing the Fix

To test the fix with debug validation:

1. Enable debug mode in the golden model
2. Run the test_noc.c test case
3. Look for the following in the logs:
   - "[NoC] Found matching pair" message
   - "[NoC Validation]" messages showing data transfer
   - "Transfer COMPLETE" message

## Expected Output

With debug enabled, you should see:
```
[NoC] Handling NOC_SRC primitive 2
      Executing on Core: Group 0 ID 0
      Source: Group None ID None
      Dest: Group [0, 0, 0, 0] ID 1
[NoC] Handling NOC_DEST primitive 4
      Executing on Core: Group 0 ID 1
      Source: Group [0, 0, 0, 0] ID 0
      Dest: Group None ID None
[NoC] Found matching pair: NOC_DEST:4 <-> NOC_SRC:2
      Executing NoC transfer pair
[NoC] Dispatching paired transfer:
      SRC primitive 2 on Core G0:C0
      DEST primitive 4 on Core G0:C1
      Transfer: Core[0] -> Core[1]
      Address: 0x00000000 -> 0x00000000
[NoC Validation] SRC 2 read XXX bytes from Core 0
[NoC Validation] DEST 4 wrote XXX bytes to Core 1
[NoC Validation] Transfer COMPLETE: NOC_2_4
```

---
*Fix implemented: 2025-08-04*