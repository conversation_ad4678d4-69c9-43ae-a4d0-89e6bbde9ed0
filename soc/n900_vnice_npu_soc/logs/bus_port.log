[08-11 16:49:45.592] [0 s] axi-master n900_vnice_npu_soc.n900_vnice.mem__128_axi_m binding device n900_vnice_npu_soc.bus.s_0
[08-11 16:49:45.594] [0 s] nice-master n900_vnice_npu_soc.n900_vnice.nice_m__32 binding dev n900_vnice_npu_soc.nice_remote_adapter.nice
[08-11 16:49:45.596] [0 s] vnice-master n900_vnice_npu_soc.n900_vnice.vnice_m__32 binding dev n900_vnice_npu_soc.nice_remote_adapter.vnice
[08-11 16:49:45.599] [0 s] axi-slave stub n900_vnice_npu_soc.n900_vnice.Vn900_core_rams port n900_vnice_npu_soc.n900_vnice.slv__64_axi_s
[08-11 16:49:45.601] [0 s] ------- load n900_vnice_npu_soc.bus route rules: -------
[08-11 16:49:45.601] [0 s] n900_vnice_npu_soc.bus rule_0: slave0 to master0 n900_vnice_npu_soc.acc0 addr_range: start 0x100000 -- end 0x1fffff, length 0x100000
[08-11 16:49:45.601] [0 s] n900_vnice_npu_soc.bus rule_1: slave0 to master1 n900_vnice_npu_soc.mrom addr_range: start 0x0 -- end 0xfffff, length 0x100000
[08-11 16:49:45.601] [0 s] n900_vnice_npu_soc.bus rule_2: slave0 to master2 n900_vnice_npu_soc.gpio addr_range: start 0x10012000 -- end 0x10012fff, length 0x1000
[08-11 16:49:45.601] [0 s] n900_vnice_npu_soc.bus rule_3: slave0 to master3 n900_vnice_npu_soc.uart addr_range: start 0x10013000 -- end 0x10013fff, length 0x1000
[08-11 16:49:45.601] [0 s] n900_vnice_npu_soc.bus rule_4: slave0 to master4 n900_vnice_npu_soc.qspi0 addr_range: start 0x10014000 -- end 0x10023fff, length 0x10000
[08-11 16:49:45.601] [0 s] n900_vnice_npu_soc.bus rule_5: slave0 to master5 n900_vnice_npu_soc.qspi1 addr_range: start 0x60000000 -- end 0x6effffff, length 0xf000000
[08-11 16:49:45.601] [0 s] n900_vnice_npu_soc.bus rule_6: slave0 to master6 n900_vnice_npu_soc.hole addr_range: start 0x50000000 -- end 0x5fedffff, length 0xfee0000
[08-11 16:49:45.601] [0 s] n900_vnice_npu_soc.bus rule_7: slave0 to master7 n900_vnice_npu_soc.xip addr_range: start 0x20000000 -- end 0x3fffffff, length 0x20000000
[08-11 16:49:45.601] [0 s] n900_vnice_npu_soc.bus rule_8: slave0 to master8 n900_vnice_npu_soc.ddr addr_range: start 0x70180000 -- end 0x9017ffff, length 0x20000000
[08-11 16:49:45.601] [0 s] n900_vnice_npu_soc.bus rule_9: slave0 to master9 n900_vnice_npu_soc.acc1 addr_range: start 0x10033000 -- end 0x10033fff, length 0x1000
[08-11 16:49:45.601] [0 s] n900_vnice_npu_soc.bus rule_10: slave0 to master10 n900_vnice_npu_soc.n900_vnice addr_range: start 0x70000000 -- end 0x7017ffff, length 0x180000
[08-11 16:49:45.601] [0 s] n900_vnice_npu_soc.bus rule_11: slave1 to master9 n900_vnice_npu_soc.acc1 addr_range: start 0x10033000 -- end 0x10033fff, length 0x1000
[08-11 16:49:46.761] [0 s] acc0 ------ load_images -------
[08-11 16:49:46.761] [0 s] acc1 ------ load_images -------
[08-11 16:49:46.761] [0 s] bus ------ load_images -------
[08-11 16:49:46.761] [0 s] ddr ------ load_images -------
[08-11 16:49:46.761] [0 s] gpio ------ load_images -------
[08-11 16:49:46.761] [0 s] hole ------ load_images -------
[08-11 16:49:46.761] [0 s] mrom ------ load_images -------
[08-11 16:49:46.761] [0 s] n900_vnice ------ load_images -------
[08-11 16:49:46.761] [0 s] nice_remote_adapter ------ load_images -------
[08-11 16:49:46.761] [0 s] qspi0 ------ load_images -------
[08-11 16:49:46.761] [0 s] qspi1 ------ load_images -------
[08-11 16:49:46.761] [0 s] xip ------ load_images -------
[08-11 16:49:46.766] [40 ns] mem__128_axi_m_0_bridge ------ load_images -------
[08-11 16:49:46.766] [40 ns] load_images: ./fw/operator_work.elf offset 0x0
[08-11 16:49:46.766] [40 ns] load_elf: file ./fw/operator_work.elf with 3 segments
[08-11 16:49:46.766] [40 ns] load_elf: segment 0x70000000         .. 0x700029a7        
[08-11 16:49:46.766] [40 ns] load_elf: segment 0x70100000         .. 0x7010103f        
[08-11 16:49:46.766] [40 ns] load_elf: segment 0x70101040         .. 0x7010183f        
[08-11 16:49:46.766] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:47.051] [6180 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013018, tr: 0x7fc1aa4402b0, reqid: 0
[08-11 16:49:47.051] [6180 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa000650
[08-11 16:49:47.051] [6180 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:47.051] [6180 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:47.051] [6180 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:47.051] [6180 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013018, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1aa000650
[08-11 16:49:47.051] [6180 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x18 data 0x0 len 0x4
[08-11 16:49:47.051] [6180 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x18 len 0x4  in pv mode
[08-11 16:49:47.052] [6180 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013018 data 0x0 len 0x4
[08-11 16:49:47.052] [6180 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 0
[08-11 16:49:47.052] [6180 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:47.052] [6182 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:47.052] [6182 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:47.053] [6206 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013008, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 0
[08-11 16:49:47.053] [6206 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:47.053] [6206 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x8 data 0xaa0018c0 len 0x4
[08-11 16:49:47.053] [6206 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x8 len 0x4  in pv mode
[08-11 16:49:47.053] [6206 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013008 data 0x0 len 0x4
[08-11 16:49:47.053] [6206 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 0
[08-11 16:49:47.053] [6206 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:47.053] [6206 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.053] [6206 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.053] [6206 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.053] [6206 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.053] [6206 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:47.054] [6234 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:49:47.054] [6234 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:47.054] [6234 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:47.054] [6234 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:47.054] [6234 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013008, tr: 0x7fc1a8173ed0, reqid: 1
[08-11 16:49:47.054] [6234 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013008, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:49:47.054] [6234 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x8 data 0x1 len 0x4
[08-11 16:49:47.054] [6234 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x8 len 0x4  in pv mode
[08-11 16:49:47.054] [6234 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013008 data 0x1 len 0x4
[08-11 16:49:47.054] [6234 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 1
[08-11 16:49:47.054] [6234 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:47.054] [6236 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:47.054] [6236 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:49:47.055] [6262 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x1001300c, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 1
[08-11 16:49:47.055] [6262 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:47.055] [6262 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0xc data 0xaa002050 len 0x4
[08-11 16:49:47.055] [6262 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0xc len 0x4  in pv mode
[08-11 16:49:47.055] [6262 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x1001300c data 0x0 len 0x4
[08-11 16:49:47.055] [6262 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 1
[08-11 16:49:47.055] [6262 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:47.055] [6262 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.055] [6262 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.055] [6262 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.055] [6262 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.055] [6262 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:47.056] [6290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:47.056] [6290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:47.056] [6290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:47.056] [6290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:47.056] [6290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 1001300c, tr: 0x7fc1aa4402b0, reqid: 2
[08-11 16:49:47.056] [6290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x1001300c, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:47.056] [6290 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0xc data 0x1 len 0x4
[08-11 16:49:47.056] [6290 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0xc len 0x4  in pv mode
[08-11 16:49:47.056] [6290 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x1001300c data 0x1 len 0x4
[08-11 16:49:47.056] [6290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 2
[08-11 16:49:47.056] [6290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:47.057] [6292 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:47.057] [6292 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:47.593] [17620 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 2
[08-11 16:49:47.593] [17620 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:47.593] [17620 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa9fec010 len 0x4
[08-11 16:49:47.593] [17620 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.593] [17620 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:47.593] [17620 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 2
[08-11 16:49:47.593] [17620 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:47.593] [17620 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.593] [17620 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.593] [17620 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.593] [17620 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.593] [17620 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:47.595] [17648 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 3
[08-11 16:49:47.595] [17648 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:47.595] [17648 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:47.595] [17648 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:47.595] [17648 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:47.595] [17648 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:47.595] [17648 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4e len 0x4
[08-11 16:49:47.595] [17648 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.595] [17648 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x4e len 0x4
[08-11 16:49:47.595] [17648 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 3
[08-11 16:49:47.595] [17648 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:47.595] [17650 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:47.595] [17650 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:47.604] [17852 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 3
[08-11 16:49:47.604] [17852 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:47.604] [17852 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa9fedd10 len 0x4
[08-11 16:49:47.604] [17852 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.604] [17852 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:47.604] [17852 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 3
[08-11 16:49:47.604] [17852 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:47.604] [17852 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.604] [17852 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.604] [17852 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.604] [17852 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.604] [17852 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:47.605] [17880 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 4
[08-11 16:49:47.605] [17880 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:47.605] [17880 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:47.605] [17880 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:47.605] [17880 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:47.605] [17880 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:47.605] [17880 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x75 len 0x4
[08-11 16:49:47.605] [17880 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.605] [17880 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x75 len 0x4
[08-11 16:49:47.605] [17880 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 4
[08-11 16:49:47.605] [17880 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:47.605] [17882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:47.605] [17882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:47.614] [18084 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 4
[08-11 16:49:47.614] [18084 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:47.614] [18084 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa1223a0 len 0x4
[08-11 16:49:47.614] [18084 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.614] [18084 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:47.614] [18084 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 4
[08-11 16:49:47.614] [18084 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:47.614] [18084 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.614] [18084 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.614] [18084 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.614] [18084 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.614] [18084 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:47.616] [18112 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 5
[08-11 16:49:47.616] [18112 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:47.616] [18112 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:47.616] [18112 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:47.616] [18112 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:47.616] [18112 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:47.616] [18112 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x63 len 0x4
[08-11 16:49:47.616] [18112 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.616] [18112 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x63 len 0x4
[08-11 16:49:47.616] [18112 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 5
[08-11 16:49:47.616] [18112 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:47.616] [18114 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:47.616] [18114 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:47.625] [18316 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 5
[08-11 16:49:47.625] [18316 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:47.625] [18316 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa1240a0 len 0x4
[08-11 16:49:47.625] [18316 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.625] [18316 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:47.625] [18316 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 5
[08-11 16:49:47.625] [18316 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:47.625] [18316 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.625] [18316 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.625] [18316 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.625] [18316 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.625] [18316 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:47.626] [18344 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 6
[08-11 16:49:47.626] [18344 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:47.626] [18344 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:47.626] [18344 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:47.626] [18344 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:47.626] [18344 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:47.626] [18344 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6c len 0x4
[08-11 16:49:47.626] [18344 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.626] [18344 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6c len 0x4
[08-11 16:49:47.626] [18344 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 6
[08-11 16:49:47.626] [18344 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:47.626] [18346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:47.626] [18346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:47.635] [18548 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 6
[08-11 16:49:47.635] [18548 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:47.635] [18548 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa125da0 len 0x4
[08-11 16:49:47.635] [18548 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.635] [18548 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:47.635] [18548 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 6
[08-11 16:49:47.635] [18548 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:47.635] [18548 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.635] [18548 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.635] [18548 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.635] [18548 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.635] [18548 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:47.637] [18576 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 7
[08-11 16:49:47.637] [18576 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:47.637] [18576 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:47.637] [18576 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:47.637] [18576 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:47.637] [18576 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:47.637] [18576 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x65 len 0x4
[08-11 16:49:47.637] [18576 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.637] [18576 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x65 len 0x4
[08-11 16:49:47.637] [18576 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 7
[08-11 16:49:47.637] [18576 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:47.637] [18578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:47.637] [18578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:47.646] [18780 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 7
[08-11 16:49:47.646] [18780 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:47.646] [18780 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa127aa0 len 0x4
[08-11 16:49:47.646] [18780 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.646] [18780 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:47.646] [18780 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 7
[08-11 16:49:47.646] [18780 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:47.646] [18780 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.646] [18780 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.646] [18780 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.646] [18780 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.646] [18780 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:47.647] [18808 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 8
[08-11 16:49:47.647] [18808 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:47.647] [18808 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:47.647] [18808 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:47.647] [18808 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:47.647] [18808 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:47.647] [18808 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x69 len 0x4
[08-11 16:49:47.647] [18808 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.647] [18808 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x69 len 0x4
[08-11 16:49:47.647] [18808 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 8
[08-11 16:49:47.647] [18808 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:47.648] [18810 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:47.648] [18810 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:47.657] [19012 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 8
[08-11 16:49:47.657] [19012 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:47.657] [19012 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa1297a0 len 0x4
[08-11 16:49:47.657] [19012 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.657] [19012 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:47.657] [19012 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 8
[08-11 16:49:47.657] [19012 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:47.657] [19012 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.657] [19012 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.657] [19012 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.657] [19012 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.657] [19012 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:47.658] [19040 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 9
[08-11 16:49:47.658] [19040 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:47.658] [19040 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:47.658] [19040 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:47.658] [19040 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:47.658] [19040 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:47.658] [19040 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-11 16:49:47.658] [19040 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.658] [19040 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-11 16:49:47.658] [19040 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 9
[08-11 16:49:47.658] [19040 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:47.658] [19042 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:47.658] [19042 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:47.667] [19244 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 9
[08-11 16:49:47.667] [19244 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:47.667] [19244 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa12b4a0 len 0x4
[08-11 16:49:47.667] [19244 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.667] [19244 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:47.667] [19244 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 9
[08-11 16:49:47.667] [19244 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:47.667] [19244 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.667] [19244 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.667] [19244 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.667] [19244 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.667] [19244 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:47.668] [19272 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 10
[08-11 16:49:47.668] [19272 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:47.668] [19272 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:47.668] [19272 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:47.668] [19272 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:47.668] [19272 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:47.668] [19272 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x53 len 0x4
[08-11 16:49:47.668] [19272 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.668] [19272 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x53 len 0x4
[08-11 16:49:47.668] [19272 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 10
[08-11 16:49:47.668] [19272 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:47.669] [19274 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:47.669] [19274 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:47.678] [19476 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 10
[08-11 16:49:47.678] [19476 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:47.678] [19476 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa12d1a0 len 0x4
[08-11 16:49:47.678] [19476 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.678] [19476 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:47.678] [19476 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 10
[08-11 16:49:47.678] [19476 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:47.678] [19476 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.678] [19476 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.678] [19476 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.678] [19476 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.678] [19476 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:47.679] [19504 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 11
[08-11 16:49:47.679] [19504 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:47.679] [19504 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:47.679] [19504 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:47.679] [19504 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:47.679] [19504 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:47.679] [19504 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x44 len 0x4
[08-11 16:49:47.679] [19504 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.679] [19504 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x44 len 0x4
[08-11 16:49:47.679] [19504 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 11
[08-11 16:49:47.679] [19504 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:47.679] [19506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:47.679] [19506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:47.688] [19708 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 11
[08-11 16:49:47.688] [19708 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:47.688] [19708 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa12eea0 len 0x4
[08-11 16:49:47.688] [19708 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.688] [19708 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:47.688] [19708 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 11
[08-11 16:49:47.688] [19708 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:47.688] [19708 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.688] [19708 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.688] [19708 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.688] [19708 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.688] [19708 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:47.690] [19736 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 12
[08-11 16:49:47.690] [19736 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:47.690] [19736 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:47.690] [19736 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:47.690] [19736 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:47.690] [19736 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:47.690] [19736 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4b len 0x4
[08-11 16:49:47.690] [19736 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.690] [19736 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x4b len 0x4
[08-11 16:49:47.690] [19736 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 12
[08-11 16:49:47.690] [19736 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:47.690] [19738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:47.690] [19738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:47.699] [19940 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 12
[08-11 16:49:47.699] [19940 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:47.699] [19940 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa130ba0 len 0x4
[08-11 16:49:47.699] [19940 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.699] [19940 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:47.699] [19940 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 12
[08-11 16:49:47.699] [19940 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:47.699] [19940 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.699] [19940 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.699] [19940 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.699] [19940 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.699] [19940 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:47.700] [19968 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 13
[08-11 16:49:47.700] [19968 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:47.700] [19968 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:47.700] [19968 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:47.700] [19968 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:47.700] [19968 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:47.700] [19968 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-11 16:49:47.700] [19968 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.700] [19968 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-11 16:49:47.700] [19968 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 13
[08-11 16:49:47.700] [19968 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:47.700] [19970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:47.700] [19970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:47.710] [20172 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 13
[08-11 16:49:47.710] [20172 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:47.710] [20172 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa1328a0 len 0x4
[08-11 16:49:47.710] [20172 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.710] [20172 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:47.710] [20172 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 13
[08-11 16:49:47.710] [20172 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:47.710] [20172 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.710] [20172 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.710] [20172 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.710] [20172 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.710] [20172 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:47.711] [20200 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 14
[08-11 16:49:47.711] [20200 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:47.711] [20200 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:47.711] [20200 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:47.711] [20200 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:47.711] [20200 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:47.711] [20200 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x42 len 0x4
[08-11 16:49:47.711] [20200 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.711] [20200 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x42 len 0x4
[08-11 16:49:47.711] [20200 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 14
[08-11 16:49:47.711] [20200 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:47.711] [20202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:47.711] [20202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:47.720] [20404 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 14
[08-11 16:49:47.720] [20404 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:47.720] [20404 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa1345a0 len 0x4
[08-11 16:49:47.720] [20404 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.720] [20404 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:47.720] [20404 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 14
[08-11 16:49:47.720] [20404 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:47.720] [20404 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.720] [20404 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.720] [20404 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.720] [20404 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.720] [20404 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:47.722] [20432 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 15
[08-11 16:49:47.722] [20432 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:47.722] [20432 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:47.722] [20432 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:47.722] [20432 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:47.722] [20432 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:47.722] [20432 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x75 len 0x4
[08-11 16:49:47.722] [20432 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.722] [20432 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x75 len 0x4
[08-11 16:49:47.722] [20432 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 15
[08-11 16:49:47.722] [20432 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:47.722] [20434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:47.722] [20434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:47.731] [20636 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 15
[08-11 16:49:47.731] [20636 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:47.731] [20636 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa1362a0 len 0x4
[08-11 16:49:47.731] [20636 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.731] [20636 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:47.731] [20636 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 15
[08-11 16:49:47.731] [20636 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:47.731] [20636 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.731] [20636 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.731] [20636 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.731] [20636 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.731] [20636 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:47.732] [20664 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 16
[08-11 16:49:47.732] [20664 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:47.732] [20664 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:47.732] [20664 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:47.732] [20664 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:47.732] [20664 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:47.732] [20664 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x69 len 0x4
[08-11 16:49:47.732] [20664 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.732] [20664 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x69 len 0x4
[08-11 16:49:47.732] [20664 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 16
[08-11 16:49:47.732] [20664 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:47.732] [20666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:47.732] [20666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:47.742] [20868 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 16
[08-11 16:49:47.742] [20868 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:47.742] [20868 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa137fa0 len 0x4
[08-11 16:49:47.742] [20868 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.742] [20868 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:47.742] [20868 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 16
[08-11 16:49:47.742] [20868 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:47.742] [20868 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.742] [20868 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.742] [20868 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.742] [20868 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.742] [20868 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:47.743] [20896 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 17
[08-11 16:49:47.743] [20896 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:47.743] [20896 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:47.743] [20896 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:47.743] [20896 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:47.743] [20896 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:47.743] [20896 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6c len 0x4
[08-11 16:49:47.743] [20896 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.743] [20896 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6c len 0x4
[08-11 16:49:47.743] [20896 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 17
[08-11 16:49:47.743] [20896 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:47.743] [20898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:47.743] [20898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:47.752] [21100 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 17
[08-11 16:49:47.752] [21100 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:47.752] [21100 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa139ca0 len 0x4
[08-11 16:49:47.752] [21100 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.752] [21100 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:47.752] [21100 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 17
[08-11 16:49:47.752] [21100 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:47.752] [21100 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.752] [21100 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.752] [21100 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.752] [21100 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.752] [21100 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:47.753] [21128 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 18
[08-11 16:49:47.753] [21128 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:47.753] [21128 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:47.753] [21128 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:47.753] [21128 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:47.753] [21128 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:47.753] [21128 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x64 len 0x4
[08-11 16:49:47.753] [21128 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.753] [21128 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x64 len 0x4
[08-11 16:49:47.753] [21128 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 18
[08-11 16:49:47.753] [21128 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:47.754] [21130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:47.754] [21130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:47.763] [21332 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 18
[08-11 16:49:47.763] [21332 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:47.763] [21332 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa13b9a0 len 0x4
[08-11 16:49:47.763] [21332 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.763] [21332 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:47.763] [21332 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 18
[08-11 16:49:47.763] [21332 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:47.763] [21332 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.763] [21332 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.763] [21332 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.763] [21332 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.763] [21332 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:47.764] [21360 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 19
[08-11 16:49:47.764] [21360 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:47.764] [21360 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:47.764] [21360 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:47.764] [21360 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:47.764] [21360 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:47.764] [21360 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-11 16:49:47.764] [21360 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.764] [21360 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-11 16:49:47.764] [21360 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 19
[08-11 16:49:47.764] [21360 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:47.764] [21362 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:47.764] [21362 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:47.774] [21564 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 19
[08-11 16:49:47.774] [21564 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:47.774] [21564 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa13d6a0 len 0x4
[08-11 16:49:47.774] [21564 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.774] [21564 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:47.774] [21564 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 19
[08-11 16:49:47.774] [21564 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:47.774] [21564 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.774] [21564 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.774] [21564 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.774] [21564 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.774] [21564 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:47.775] [21592 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 20
[08-11 16:49:47.775] [21592 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:47.775] [21592 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:47.775] [21592 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:47.775] [21592 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:47.775] [21592 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:47.775] [21592 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x54 len 0x4
[08-11 16:49:47.775] [21592 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.775] [21592 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x54 len 0x4
[08-11 16:49:47.775] [21592 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 20
[08-11 16:49:47.775] [21592 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:47.775] [21594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:47.775] [21594 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:47.784] [21796 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 20
[08-11 16:49:47.784] [21796 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:47.784] [21796 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa13f3a0 len 0x4
[08-11 16:49:47.784] [21796 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.784] [21796 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:47.784] [21796 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 20
[08-11 16:49:47.784] [21796 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:47.784] [21796 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.784] [21796 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.784] [21796 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.784] [21796 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.784] [21796 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:47.785] [21824 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 21
[08-11 16:49:47.785] [21824 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:47.785] [21824 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:47.785] [21824 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:47.785] [21824 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:47.785] [21824 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:47.785] [21824 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x69 len 0x4
[08-11 16:49:47.785] [21824 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.785] [21824 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x69 len 0x4
[08-11 16:49:47.785] [21824 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 21
[08-11 16:49:47.785] [21824 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:47.786] [21826 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:47.786] [21826 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:47.795] [22028 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 21
[08-11 16:49:47.795] [22028 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:47.795] [22028 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa1410a0 len 0x4
[08-11 16:49:47.795] [22028 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.795] [22028 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:47.795] [22028 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 21
[08-11 16:49:47.795] [22028 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:47.795] [22028 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.795] [22028 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.795] [22028 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.795] [22028 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.795] [22028 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:47.796] [22056 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 22
[08-11 16:49:47.796] [22056 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:47.796] [22056 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:47.796] [22056 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:47.796] [22056 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:47.796] [22056 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:47.796] [22056 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6d len 0x4
[08-11 16:49:47.796] [22056 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.796] [22056 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6d len 0x4
[08-11 16:49:47.796] [22056 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 22
[08-11 16:49:47.796] [22056 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:47.796] [22058 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:47.796] [22058 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:47.808] [22260 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 22
[08-11 16:49:47.808] [22260 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:47.808] [22260 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa037220 len 0x4
[08-11 16:49:47.808] [22260 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.808] [22260 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:47.808] [22260 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 22
[08-11 16:49:47.808] [22260 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:47.808] [22260 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.808] [22260 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.808] [22260 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.808] [22260 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.808] [22260 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:47.809] [22288 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 23
[08-11 16:49:47.809] [22288 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:47.809] [22288 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:47.809] [22288 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:47.809] [22288 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:47.809] [22288 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:47.809] [22288 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x65 len 0x4
[08-11 16:49:47.809] [22288 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.809] [22288 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x65 len 0x4
[08-11 16:49:47.809] [22288 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 23
[08-11 16:49:47.809] [22288 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:47.810] [22290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:47.810] [22290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:47.819] [22492 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 23
[08-11 16:49:47.819] [22492 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:47.819] [22492 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa038f20 len 0x4
[08-11 16:49:47.819] [22492 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.819] [22492 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:47.819] [22492 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 23
[08-11 16:49:47.819] [22492 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:47.819] [22492 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.819] [22492 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.819] [22492 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.819] [22492 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.819] [22492 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:47.821] [22520 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 24
[08-11 16:49:47.821] [22520 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:47.821] [22520 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:47.821] [22520 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:47.821] [22520 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:47.821] [22520 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:47.821] [22520 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x3a len 0x4
[08-11 16:49:47.821] [22520 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.821] [22520 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x3a len 0x4
[08-11 16:49:47.821] [22520 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 24
[08-11 16:49:47.821] [22520 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:47.821] [22522 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:47.821] [22522 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:47.830] [22724 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 24
[08-11 16:49:47.830] [22724 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:47.830] [22724 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa03ac20 len 0x4
[08-11 16:49:47.830] [22724 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.831] [22724 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:47.831] [22724 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 24
[08-11 16:49:47.831] [22724 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:47.831] [22724 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.831] [22724 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.831] [22724 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.831] [22724 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.831] [22724 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:47.832] [22752 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 25
[08-11 16:49:47.832] [22752 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:47.832] [22752 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:47.832] [22752 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:47.832] [22752 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:47.832] [22752 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:47.832] [22752 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-11 16:49:47.832] [22752 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.832] [22752 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-11 16:49:47.832] [22752 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 25
[08-11 16:49:47.832] [22752 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:47.832] [22754 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:47.832] [22754 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:47.841] [22956 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 25
[08-11 16:49:47.841] [22956 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:47.841] [22956 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa03c920 len 0x4
[08-11 16:49:47.841] [22956 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.841] [22956 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:47.841] [22956 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 25
[08-11 16:49:47.841] [22956 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:47.841] [22956 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.841] [22956 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.841] [22956 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.841] [22956 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.841] [22956 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:47.843] [22984 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 26
[08-11 16:49:47.843] [22984 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:47.843] [22984 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:47.843] [22984 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:47.843] [22984 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:47.843] [22984 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:47.843] [22984 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x41 len 0x4
[08-11 16:49:47.843] [22984 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.843] [22984 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x41 len 0x4
[08-11 16:49:47.843] [22984 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 26
[08-11 16:49:47.843] [22984 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:47.843] [22986 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:47.843] [22986 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:47.852] [23188 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 26
[08-11 16:49:47.852] [23188 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:47.852] [23188 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa03e620 len 0x4
[08-11 16:49:47.852] [23188 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.852] [23188 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:47.852] [23188 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 26
[08-11 16:49:47.852] [23188 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:47.852] [23188 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.852] [23188 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.852] [23188 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.852] [23188 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.852] [23188 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:47.854] [23216 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 27
[08-11 16:49:47.854] [23216 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:47.854] [23216 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:47.854] [23216 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:47.854] [23216 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:47.854] [23216 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:47.854] [23216 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x75 len 0x4
[08-11 16:49:47.854] [23216 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.854] [23216 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x75 len 0x4
[08-11 16:49:47.854] [23216 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 27
[08-11 16:49:47.854] [23216 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:47.854] [23218 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:47.854] [23218 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:47.863] [23420 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 27
[08-11 16:49:47.863] [23420 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:47.863] [23420 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa040320 len 0x4
[08-11 16:49:47.863] [23420 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.863] [23420 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:47.863] [23420 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 27
[08-11 16:49:47.863] [23420 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:47.863] [23420 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.863] [23420 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.863] [23420 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.863] [23420 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.863] [23420 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:47.864] [23448 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 28
[08-11 16:49:47.864] [23448 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:47.864] [23448 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:47.864] [23448 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:47.864] [23448 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:47.864] [23448 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:47.864] [23448 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x67 len 0x4
[08-11 16:49:47.864] [23448 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.864] [23448 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x67 len 0x4
[08-11 16:49:47.864] [23448 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 28
[08-11 16:49:47.864] [23448 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:47.865] [23450 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:47.865] [23450 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:47.874] [23652 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 28
[08-11 16:49:47.874] [23652 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:47.874] [23652 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa042020 len 0x4
[08-11 16:49:47.874] [23652 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.874] [23652 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:47.874] [23652 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 28
[08-11 16:49:47.874] [23652 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:47.874] [23652 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.874] [23652 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.874] [23652 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.874] [23652 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.874] [23652 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:47.875] [23680 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 29
[08-11 16:49:47.875] [23680 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:47.875] [23680 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:47.875] [23680 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:47.875] [23680 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:47.875] [23680 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:47.875] [23680 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-11 16:49:47.875] [23680 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.875] [23680 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-11 16:49:47.875] [23680 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 29
[08-11 16:49:47.875] [23680 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:47.875] [23682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:47.875] [23682 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:47.885] [23884 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 29
[08-11 16:49:47.885] [23884 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:47.885] [23884 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa043d20 len 0x4
[08-11 16:49:47.885] [23884 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.885] [23884 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:47.885] [23884 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 29
[08-11 16:49:47.885] [23884 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:47.885] [23884 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.885] [23884 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.885] [23884 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.885] [23884 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.885] [23884 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:47.886] [23912 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 30
[08-11 16:49:47.886] [23912 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:47.886] [23912 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:47.886] [23912 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:47.886] [23912 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:47.886] [23912 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:47.886] [23912 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x31 len 0x4
[08-11 16:49:47.886] [23912 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.886] [23912 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x31 len 0x4
[08-11 16:49:47.886] [23912 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 30
[08-11 16:49:47.886] [23912 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:47.886] [23914 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:47.886] [23914 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:47.896] [24116 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 30
[08-11 16:49:47.896] [24116 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:47.896] [24116 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa045a20 len 0x4
[08-11 16:49:47.896] [24116 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.896] [24116 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:47.896] [24116 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 30
[08-11 16:49:47.896] [24116 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:47.896] [24116 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.896] [24116 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.896] [24116 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.896] [24116 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.896] [24116 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:47.897] [24144 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 31
[08-11 16:49:47.897] [24144 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:47.897] [24144 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:47.897] [24144 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:47.897] [24144 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:47.897] [24144 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:47.897] [24144 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x31 len 0x4
[08-11 16:49:47.897] [24144 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.897] [24144 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x31 len 0x4
[08-11 16:49:47.897] [24144 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 31
[08-11 16:49:47.897] [24144 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:47.897] [24146 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:47.897] [24146 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:47.907] [24348 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 31
[08-11 16:49:47.907] [24348 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:47.907] [24348 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa047720 len 0x4
[08-11 16:49:47.907] [24348 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.907] [24348 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:47.907] [24348 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 31
[08-11 16:49:47.907] [24348 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:47.907] [24348 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.907] [24348 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.907] [24348 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.907] [24348 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.907] [24348 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:47.908] [24376 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 32
[08-11 16:49:47.908] [24376 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:47.908] [24376 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:47.908] [24376 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:47.908] [24376 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:47.908] [24376 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:47.908] [24376 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-11 16:49:47.908] [24376 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.908] [24376 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-11 16:49:47.908] [24376 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 32
[08-11 16:49:47.908] [24376 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:47.908] [24378 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:47.908] [24378 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:47.917] [24580 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 32
[08-11 16:49:47.917] [24580 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:47.917] [24580 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa049420 len 0x4
[08-11 16:49:47.917] [24580 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.917] [24580 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:47.917] [24580 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 32
[08-11 16:49:47.917] [24580 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:47.917] [24580 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.917] [24580 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.917] [24580 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.917] [24580 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.917] [24580 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:47.919] [24608 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 33
[08-11 16:49:47.919] [24608 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:47.919] [24608 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:47.919] [24608 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:47.919] [24608 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:47.919] [24608 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:47.919] [24608 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x32 len 0x4
[08-11 16:49:47.919] [24608 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.919] [24608 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x32 len 0x4
[08-11 16:49:47.919] [24608 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 33
[08-11 16:49:47.919] [24608 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:47.919] [24610 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:47.919] [24610 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:47.928] [24812 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 33
[08-11 16:49:47.928] [24812 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:47.928] [24812 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa04b120 len 0x4
[08-11 16:49:47.928] [24812 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.928] [24812 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:47.928] [24812 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 33
[08-11 16:49:47.928] [24812 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:47.928] [24812 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.928] [24812 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.928] [24812 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.928] [24812 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.928] [24812 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:47.930] [24840 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 34
[08-11 16:49:47.930] [24840 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:47.930] [24840 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:47.930] [24840 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:47.930] [24840 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:47.930] [24840 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:47.930] [24840 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x30 len 0x4
[08-11 16:49:47.930] [24840 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.930] [24840 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x30 len 0x4
[08-11 16:49:47.930] [24840 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 34
[08-11 16:49:47.930] [24840 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:47.930] [24842 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:47.930] [24842 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:47.939] [25044 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 34
[08-11 16:49:47.939] [25044 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:47.939] [25044 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa04ce20 len 0x4
[08-11 16:49:47.939] [25044 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.939] [25044 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:47.939] [25044 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 34
[08-11 16:49:47.939] [25044 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:47.939] [25044 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.939] [25044 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.939] [25044 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.939] [25044 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.939] [25044 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:47.941] [25072 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 35
[08-11 16:49:47.941] [25072 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:47.941] [25072 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:47.941] [25072 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:47.941] [25072 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:47.941] [25072 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:47.941] [25072 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x32 len 0x4
[08-11 16:49:47.941] [25072 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.941] [25072 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x32 len 0x4
[08-11 16:49:47.941] [25072 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 35
[08-11 16:49:47.941] [25072 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:47.941] [25074 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:47.941] [25074 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:47.950] [25276 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 35
[08-11 16:49:47.950] [25276 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:47.950] [25276 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa04eb20 len 0x4
[08-11 16:49:47.950] [25276 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.950] [25276 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:47.950] [25276 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 35
[08-11 16:49:47.950] [25276 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:47.950] [25276 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.950] [25276 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.950] [25276 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.950] [25276 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.950] [25276 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:47.951] [25304 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 36
[08-11 16:49:47.951] [25304 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:47.951] [25304 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:47.951] [25304 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:47.951] [25304 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:47.951] [25304 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:47.951] [25304 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x35 len 0x4
[08-11 16:49:47.951] [25304 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.951] [25304 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x35 len 0x4
[08-11 16:49:47.951] [25304 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 36
[08-11 16:49:47.951] [25304 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:47.951] [25306 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:47.951] [25306 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:47.961] [25508 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 36
[08-11 16:49:47.961] [25508 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:47.961] [25508 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa050820 len 0x4
[08-11 16:49:47.961] [25508 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.961] [25508 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:47.961] [25508 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 36
[08-11 16:49:47.961] [25508 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:47.961] [25508 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.961] [25508 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.961] [25508 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.961] [25508 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.961] [25508 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:47.962] [25536 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 37
[08-11 16:49:47.962] [25536 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:47.962] [25536 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:47.962] [25536 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:47.962] [25536 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:47.962] [25536 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:47.962] [25536 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x2c len 0x4
[08-11 16:49:47.962] [25536 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.962] [25536 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x2c len 0x4
[08-11 16:49:47.962] [25536 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 37
[08-11 16:49:47.962] [25536 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:47.962] [25538 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:47.962] [25538 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:47.972] [25740 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 37
[08-11 16:49:47.972] [25740 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:47.972] [25740 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa052520 len 0x4
[08-11 16:49:47.972] [25740 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.972] [25740 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:47.972] [25740 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 37
[08-11 16:49:47.972] [25740 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:47.972] [25740 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.972] [25740 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.972] [25740 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.972] [25740 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.972] [25740 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:47.973] [25768 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 38
[08-11 16:49:47.973] [25768 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:47.973] [25768 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:47.973] [25768 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:47.973] [25768 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:47.973] [25768 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:47.973] [25768 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-11 16:49:47.973] [25768 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.973] [25768 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-11 16:49:47.973] [25768 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 38
[08-11 16:49:47.973] [25768 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:47.973] [25770 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:47.973] [25770 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:47.983] [25972 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 38
[08-11 16:49:47.983] [25972 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:47.983] [25972 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa054220 len 0x4
[08-11 16:49:47.983] [25972 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.983] [25972 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:47.983] [25972 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 38
[08-11 16:49:47.983] [25972 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:47.983] [25972 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.983] [25972 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.983] [25972 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.983] [25972 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.983] [25972 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:47.984] [26 us] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 39
[08-11 16:49:47.984] [26 us] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:47.984] [26 us] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:47.984] [26 us] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:47.984] [26 us] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:47.984] [26 us] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:47.984] [26 us] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x31 len 0x4
[08-11 16:49:47.984] [26 us] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.984] [26 us] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x31 len 0x4
[08-11 16:49:47.984] [26 us] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 39
[08-11 16:49:47.984] [26 us] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:47.984] [26002 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:47.984] [26002 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:47.993] [26204 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 39
[08-11 16:49:47.993] [26204 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:47.993] [26204 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa055f20 len 0x4
[08-11 16:49:47.993] [26204 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.993] [26204 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:47.993] [26204 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 39
[08-11 16:49:47.993] [26204 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:47.993] [26204 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.993] [26204 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.993] [26204 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.993] [26204 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:47.993] [26204 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:47.995] [26232 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 40
[08-11 16:49:47.995] [26232 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:47.995] [26232 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:47.995] [26232 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:47.995] [26232 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:47.995] [26232 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:47.995] [26232 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x36 len 0x4
[08-11 16:49:47.995] [26232 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:47.995] [26232 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x36 len 0x4
[08-11 16:49:47.995] [26232 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 40
[08-11 16:49:47.995] [26232 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:47.995] [26234 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:47.995] [26234 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:48.004] [26436 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 40
[08-11 16:49:48.004] [26436 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:48.004] [26436 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa057c20 len 0x4
[08-11 16:49:48.004] [26436 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.004] [26436 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:48.004] [26436 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 40
[08-11 16:49:48.004] [26436 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:48.004] [26436 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.004] [26436 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.004] [26436 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.004] [26436 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.004] [26436 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:48.006] [26464 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 41
[08-11 16:49:48.006] [26464 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:48.006] [26464 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:48.006] [26464 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:48.006] [26464 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:48.006] [26464 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:48.006] [26464 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x3a len 0x4
[08-11 16:49:48.006] [26464 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.006] [26464 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x3a len 0x4
[08-11 16:49:48.006] [26464 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 41
[08-11 16:49:48.006] [26464 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:48.006] [26466 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:48.006] [26466 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:48.015] [26668 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 41
[08-11 16:49:48.015] [26668 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:48.015] [26668 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa059920 len 0x4
[08-11 16:49:48.015] [26668 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.015] [26668 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:48.015] [26668 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 41
[08-11 16:49:48.015] [26668 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:48.015] [26668 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.015] [26668 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.015] [26668 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.015] [26668 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.015] [26668 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:48.016] [26696 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 42
[08-11 16:49:48.016] [26696 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:48.016] [26696 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:48.016] [26696 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:48.016] [26696 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:48.016] [26696 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:48.016] [26696 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x31 len 0x4
[08-11 16:49:48.016] [26696 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.016] [26696 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x31 len 0x4
[08-11 16:49:48.016] [26696 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 42
[08-11 16:49:48.016] [26696 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:48.017] [26698 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:48.017] [26698 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:48.026] [26900 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 42
[08-11 16:49:48.026] [26900 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:48.026] [26900 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa05b620 len 0x4
[08-11 16:49:48.026] [26900 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.026] [26900 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:48.026] [26900 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 42
[08-11 16:49:48.026] [26900 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:48.026] [26900 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.026] [26900 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.026] [26900 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.026] [26900 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.026] [26900 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:48.027] [26928 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 43
[08-11 16:49:48.027] [26928 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:48.027] [26928 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:48.027] [26928 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:48.027] [26928 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:48.027] [26928 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:48.027] [26928 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x37 len 0x4
[08-11 16:49:48.027] [26928 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.027] [26928 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x37 len 0x4
[08-11 16:49:48.027] [26928 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 43
[08-11 16:49:48.027] [26928 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:48.027] [26930 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:48.027] [26930 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:48.037] [27132 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 43
[08-11 16:49:48.037] [27132 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:48.037] [27132 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa05d320 len 0x4
[08-11 16:49:48.037] [27132 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.037] [27132 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:48.037] [27132 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 43
[08-11 16:49:48.037] [27132 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:48.037] [27132 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.037] [27132 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.037] [27132 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.037] [27132 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.037] [27132 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:48.038] [27160 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 44
[08-11 16:49:48.038] [27160 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:48.038] [27160 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:48.038] [27160 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:48.038] [27160 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:48.038] [27160 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:48.038] [27160 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x3a len 0x4
[08-11 16:49:48.038] [27160 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.038] [27160 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x3a len 0x4
[08-11 16:49:48.038] [27160 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 44
[08-11 16:49:48.038] [27160 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:48.038] [27162 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:48.038] [27162 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:48.048] [27364 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 44
[08-11 16:49:48.048] [27364 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:48.048] [27364 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa05f020 len 0x4
[08-11 16:49:48.048] [27364 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.048] [27364 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:48.048] [27364 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 44
[08-11 16:49:48.048] [27364 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:48.048] [27364 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.048] [27364 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.048] [27364 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.048] [27364 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.048] [27364 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:48.049] [27392 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 45
[08-11 16:49:48.049] [27392 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:48.049] [27392 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:48.049] [27392 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:48.049] [27392 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:48.049] [27392 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:48.049] [27392 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x31 len 0x4
[08-11 16:49:48.049] [27392 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.049] [27392 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x31 len 0x4
[08-11 16:49:48.049] [27392 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 45
[08-11 16:49:48.049] [27392 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:48.049] [27394 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:48.049] [27394 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:48.058] [27596 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 45
[08-11 16:49:48.058] [27596 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:48.058] [27596 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa060d20 len 0x4
[08-11 16:49:48.058] [27596 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.058] [27596 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:48.058] [27596 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 45
[08-11 16:49:48.058] [27596 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:48.058] [27596 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.059] [27596 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.059] [27596 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.059] [27596 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.059] [27596 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:48.060] [27624 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 46
[08-11 16:49:48.060] [27624 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:48.060] [27624 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:48.060] [27624 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:48.060] [27624 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:48.060] [27624 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:48.060] [27624 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x32 len 0x4
[08-11 16:49:48.060] [27624 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.060] [27624 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x32 len 0x4
[08-11 16:49:48.060] [27624 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 46
[08-11 16:49:48.060] [27624 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:48.060] [27626 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:48.060] [27626 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:48.083] [27828 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 46
[08-11 16:49:48.083] [27828 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:48.083] [27828 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa43ffc0 len 0x4
[08-11 16:49:48.083] [27828 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.083] [27828 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:48.083] [27828 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 46
[08-11 16:49:48.083] [27828 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:48.083] [27828 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.083] [27828 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.083] [27828 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.083] [27828 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.083] [27828 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:48.086] [27856 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 47
[08-11 16:49:48.086] [27856 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:48.086] [27856 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:48.086] [27856 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:48.086] [27856 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:48.086] [27856 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:48.086] [27856 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xd len 0x4
[08-11 16:49:48.086] [27856 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.086] [27856 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xd len 0x4
[08-11 16:49:48.086] [27856 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 47
[08-11 16:49:48.086] [27856 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:48.086] [27858 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:48.086] [27858 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:48.097] [28034 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 47
[08-11 16:49:48.097] [28034 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:48.097] [28034 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa061000 len 0x4
[08-11 16:49:48.097] [28034 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.097] [28034 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:48.097] [28034 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 47
[08-11 16:49:48.097] [28034 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:48.097] [28034 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.097] [28034 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.097] [28034 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.097] [28034 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.097] [28034 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:48.098] [28062 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:49:48.098] [28062 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:48.098] [28062 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:48.098] [28062 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:48.098] [28062 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 48
[08-11 16:49:48.098] [28062 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:49:48.098] [28062 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xd len 0x4
[08-11 16:49:48.098] [28062 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.098] [28062 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xd len 0x4
[08-11 16:49:48.098] [28062 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 48
[08-11 16:49:48.098] [28062 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:48.099] [28064 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:48.099] [28064 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:49:48.103] [28140 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 48
[08-11 16:49:48.103] [28140 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:48.103] [28140 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa3c66f0 len 0x4
[08-11 16:49:48.103] [28140 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.103] [28140 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:48.103] [28140 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 48
[08-11 16:49:48.103] [28140 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:48.103] [28140 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.103] [28140 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.103] [28140 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.103] [28140 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.103] [28140 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:48.105] [28168 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 49
[08-11 16:49:48.105] [28168 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:49:48.105] [28168 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:48.105] [28168 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:48.105] [28168 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:48.105] [28168 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:49:48.105] [28168 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa len 0x4
[08-11 16:49:48.105] [28168 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.105] [28168 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xa len 0x4
[08-11 16:49:48.105] [28168 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 49
[08-11 16:49:48.105] [28168 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:48.105] [28170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:48.105] [28170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:49:48.356] [33456 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 49
[08-11 16:49:48.356] [33456 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:48.356] [33456 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa0c2790 len 0x4
[08-11 16:49:48.356] [33456 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.356] [33456 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:48.356] [33456 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 49
[08-11 16:49:48.356] [33456 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:48.356] [33456 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.356] [33456 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.356] [33456 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.356] [33456 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.356] [33456 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:48.357] [33484 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 50
[08-11 16:49:48.357] [33484 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:49:48.357] [33484 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:48.357] [33484 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:48.357] [33484 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:48.357] [33484 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:49:48.357] [33484 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x44 len 0x4
[08-11 16:49:48.357] [33484 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.357] [33484 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x44 len 0x4
[08-11 16:49:48.357] [33484 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 50
[08-11 16:49:48.357] [33484 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:48.357] [33486 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:48.357] [33486 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:49:48.366] [33688 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 50
[08-11 16:49:48.366] [33688 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:48.366] [33688 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa0c4490 len 0x4
[08-11 16:49:48.366] [33688 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.366] [33688 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:48.366] [33688 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 50
[08-11 16:49:48.366] [33688 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:48.366] [33688 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.366] [33688 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.366] [33688 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.366] [33688 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.366] [33688 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:48.368] [33716 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 51
[08-11 16:49:48.368] [33716 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:49:48.368] [33716 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:48.368] [33716 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:48.368] [33716 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:48.368] [33716 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:49:48.368] [33716 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6f len 0x4
[08-11 16:49:48.368] [33716 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.368] [33716 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6f len 0x4
[08-11 16:49:48.368] [33716 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 51
[08-11 16:49:48.368] [33716 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:48.368] [33718 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:48.368] [33718 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:49:48.377] [33920 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 51
[08-11 16:49:48.377] [33920 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:48.377] [33920 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa062f80 len 0x4
[08-11 16:49:48.377] [33920 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.377] [33920 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:48.377] [33920 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 51
[08-11 16:49:48.377] [33920 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:48.377] [33920 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.377] [33920 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.377] [33920 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.377] [33920 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.377] [33920 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:48.378] [33948 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 52
[08-11 16:49:48.378] [33948 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:49:48.378] [33948 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:48.378] [33948 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:48.378] [33948 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:48.378] [33948 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:49:48.378] [33948 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x77 len 0x4
[08-11 16:49:48.378] [33948 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.378] [33948 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x77 len 0x4
[08-11 16:49:48.378] [33948 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 52
[08-11 16:49:48.378] [33948 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:48.378] [33950 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:48.378] [33950 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:49:48.388] [34152 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 52
[08-11 16:49:48.388] [34152 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:48.388] [34152 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa064c80 len 0x4
[08-11 16:49:48.388] [34152 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.388] [34152 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:48.388] [34152 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 52
[08-11 16:49:48.388] [34152 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:48.388] [34152 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.388] [34152 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.388] [34152 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.388] [34152 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.388] [34152 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:48.389] [34180 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 53
[08-11 16:49:48.389] [34180 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:49:48.389] [34180 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:48.389] [34180 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:48.389] [34180 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:48.389] [34180 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:49:48.389] [34180 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6e len 0x4
[08-11 16:49:48.389] [34180 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.389] [34180 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6e len 0x4
[08-11 16:49:48.389] [34180 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 53
[08-11 16:49:48.389] [34180 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:48.389] [34182 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:48.389] [34182 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:49:48.398] [34384 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 53
[08-11 16:49:48.398] [34384 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:48.398] [34384 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa066980 len 0x4
[08-11 16:49:48.398] [34384 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.398] [34384 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:48.398] [34384 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 53
[08-11 16:49:48.398] [34384 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:48.398] [34384 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.398] [34384 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.398] [34384 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.398] [34384 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.398] [34384 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:48.399] [34412 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 54
[08-11 16:49:48.399] [34412 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:49:48.399] [34412 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:48.399] [34412 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:48.399] [34412 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:48.399] [34412 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:49:48.399] [34412 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6c len 0x4
[08-11 16:49:48.399] [34412 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.399] [34412 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6c len 0x4
[08-11 16:49:48.399] [34412 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 54
[08-11 16:49:48.400] [34412 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:48.400] [34414 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:48.400] [34414 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:49:48.409] [34616 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 54
[08-11 16:49:48.409] [34616 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:48.409] [34616 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa068680 len 0x4
[08-11 16:49:48.409] [34616 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.409] [34616 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:48.409] [34616 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 54
[08-11 16:49:48.409] [34616 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:48.409] [34616 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.409] [34616 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.409] [34616 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.409] [34616 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.409] [34616 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:48.410] [34644 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 55
[08-11 16:49:48.410] [34644 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:49:48.410] [34644 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:48.410] [34644 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:48.410] [34644 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:48.410] [34644 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:49:48.410] [34644 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6f len 0x4
[08-11 16:49:48.410] [34644 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.410] [34644 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6f len 0x4
[08-11 16:49:48.410] [34644 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 55
[08-11 16:49:48.410] [34644 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:48.410] [34646 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:48.410] [34646 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:49:48.419] [34848 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 55
[08-11 16:49:48.419] [34848 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:48.419] [34848 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa06a380 len 0x4
[08-11 16:49:48.419] [34848 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.419] [34848 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:48.419] [34848 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 55
[08-11 16:49:48.419] [34848 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:48.419] [34848 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.419] [34848 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.419] [34848 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.419] [34848 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.419] [34848 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:48.421] [34876 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 56
[08-11 16:49:48.421] [34876 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:49:48.421] [34876 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:48.421] [34876 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:48.421] [34876 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:48.421] [34876 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:49:48.421] [34876 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x61 len 0x4
[08-11 16:49:48.421] [34876 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.421] [34876 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x61 len 0x4
[08-11 16:49:48.421] [34876 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 56
[08-11 16:49:48.421] [34876 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:48.421] [34878 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:48.421] [34878 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:49:48.430] [35080 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 56
[08-11 16:49:48.430] [35080 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:48.430] [35080 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa06c080 len 0x4
[08-11 16:49:48.430] [35080 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.430] [35080 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:48.430] [35080 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 56
[08-11 16:49:48.430] [35080 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:48.430] [35080 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.430] [35080 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.430] [35080 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.430] [35080 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.430] [35080 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:48.431] [35108 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 57
[08-11 16:49:48.431] [35108 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:49:48.431] [35108 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:48.431] [35108 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:48.431] [35108 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:48.431] [35108 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:49:48.431] [35108 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x64 len 0x4
[08-11 16:49:48.431] [35108 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.431] [35108 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x64 len 0x4
[08-11 16:49:48.431] [35108 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 57
[08-11 16:49:48.431] [35108 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:48.431] [35110 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:48.431] [35110 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:49:48.440] [35312 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 57
[08-11 16:49:48.440] [35312 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:48.440] [35312 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa9fe3090 len 0x4
[08-11 16:49:48.440] [35312 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.440] [35312 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:48.440] [35312 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 57
[08-11 16:49:48.440] [35312 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:48.440] [35312 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.440] [35312 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.440] [35312 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.440] [35312 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.440] [35312 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:48.442] [35340 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 58
[08-11 16:49:48.442] [35340 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:49:48.442] [35340 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:48.442] [35340 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:48.442] [35340 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:48.442] [35340 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:49:48.442] [35340 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-11 16:49:48.442] [35340 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.442] [35340 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-11 16:49:48.442] [35340 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 58
[08-11 16:49:48.442] [35340 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:48.442] [35342 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:48.442] [35342 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:49:48.451] [35544 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 58
[08-11 16:49:48.451] [35544 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:48.451] [35544 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa9fe4d90 len 0x4
[08-11 16:49:48.451] [35544 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.451] [35544 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:48.451] [35544 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 58
[08-11 16:49:48.451] [35544 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:48.451] [35544 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.451] [35544 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.451] [35544 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.451] [35544 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.451] [35544 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:48.452] [35572 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 59
[08-11 16:49:48.452] [35572 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:49:48.452] [35572 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:48.452] [35572 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:48.452] [35572 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:48.452] [35572 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:49:48.452] [35572 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4d len 0x4
[08-11 16:49:48.452] [35572 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.452] [35572 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x4d len 0x4
[08-11 16:49:48.452] [35572 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 59
[08-11 16:49:48.452] [35572 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:48.452] [35574 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:48.452] [35574 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:49:48.461] [35776 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 59
[08-11 16:49:48.461] [35776 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:48.462] [35776 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa9fe6a90 len 0x4
[08-11 16:49:48.462] [35776 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.462] [35776 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:48.462] [35776 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 59
[08-11 16:49:48.462] [35776 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:48.462] [35776 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.462] [35776 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.462] [35776 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.462] [35776 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.462] [35776 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:48.463] [35804 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 60
[08-11 16:49:48.463] [35804 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:49:48.463] [35804 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:48.463] [35804 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:48.463] [35804 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:48.463] [35804 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:49:48.463] [35804 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6f len 0x4
[08-11 16:49:48.463] [35804 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.463] [35804 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6f len 0x4
[08-11 16:49:48.463] [35804 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 60
[08-11 16:49:48.463] [35804 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:48.463] [35806 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:48.463] [35806 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:49:48.472] [36008 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 60
[08-11 16:49:48.472] [36008 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:48.472] [36008 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa9fe8770 len 0x4
[08-11 16:49:48.472] [36008 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.472] [36008 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:48.472] [36008 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 60
[08-11 16:49:48.472] [36008 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:48.472] [36008 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.472] [36008 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.472] [36008 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.472] [36008 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.472] [36008 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:48.473] [36036 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 61
[08-11 16:49:48.473] [36036 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:49:48.473] [36036 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:48.473] [36036 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:48.473] [36036 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:48.473] [36036 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:49:48.473] [36036 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x64 len 0x4
[08-11 16:49:48.473] [36036 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.473] [36036 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x64 len 0x4
[08-11 16:49:48.473] [36036 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 61
[08-11 16:49:48.473] [36036 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:48.474] [36038 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:48.474] [36038 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:49:48.483] [36240 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 61
[08-11 16:49:48.483] [36240 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:48.483] [36240 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa9fea470 len 0x4
[08-11 16:49:48.483] [36240 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.483] [36240 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:48.483] [36240 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 61
[08-11 16:49:48.483] [36240 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:48.483] [36240 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.483] [36240 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.483] [36240 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.483] [36240 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.483] [36240 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:48.484] [36268 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 62
[08-11 16:49:48.484] [36268 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:49:48.484] [36268 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:48.484] [36268 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:48.484] [36268 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:48.484] [36268 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:49:48.484] [36268 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x65 len 0x4
[08-11 16:49:48.484] [36268 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.484] [36268 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x65 len 0x4
[08-11 16:49:48.484] [36268 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 62
[08-11 16:49:48.484] [36268 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:48.484] [36270 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:48.484] [36270 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:49:48.493] [36472 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 62
[08-11 16:49:48.493] [36472 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:48.493] [36472 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa9fec170 len 0x4
[08-11 16:49:48.493] [36472 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.493] [36472 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:48.493] [36472 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 62
[08-11 16:49:48.493] [36472 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:48.493] [36472 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.493] [36472 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.493] [36472 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.493] [36472 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.493] [36472 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:48.494] [36500 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 63
[08-11 16:49:48.494] [36500 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:49:48.494] [36500 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:48.494] [36500 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:48.494] [36500 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:48.494] [36500 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:49:48.494] [36500 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x3a len 0x4
[08-11 16:49:48.494] [36500 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.494] [36500 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x3a len 0x4
[08-11 16:49:48.494] [36500 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 63
[08-11 16:49:48.494] [36500 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:48.495] [36502 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:48.495] [36502 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:49:48.504] [36704 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 63
[08-11 16:49:48.504] [36704 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:48.504] [36704 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa9fede70 len 0x4
[08-11 16:49:48.504] [36704 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.504] [36704 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:48.504] [36704 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 63
[08-11 16:49:48.504] [36704 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:48.504] [36704 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.504] [36704 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.504] [36704 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.504] [36704 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.504] [36704 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:48.505] [36732 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 64
[08-11 16:49:48.505] [36732 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:49:48.505] [36732 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:48.505] [36732 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:48.505] [36732 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:48.505] [36732 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:49:48.505] [36732 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-11 16:49:48.505] [36732 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.505] [36732 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-11 16:49:48.505] [36732 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 64
[08-11 16:49:48.505] [36732 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:48.505] [36734 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:48.505] [36734 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:49:48.514] [36936 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 64
[08-11 16:49:48.514] [36936 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:48.514] [36936 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa9ff04a0 len 0x4
[08-11 16:49:48.514] [36936 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.514] [36936 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:48.514] [36936 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 64
[08-11 16:49:48.514] [36936 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:48.514] [36936 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.514] [36936 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.514] [36936 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.514] [36936 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.514] [36936 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:48.515] [36964 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 65
[08-11 16:49:48.515] [36964 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:49:48.515] [36964 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:48.515] [36964 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:48.515] [36964 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:48.515] [36964 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:49:48.515] [36964 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x49 len 0x4
[08-11 16:49:48.515] [36964 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.515] [36964 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x49 len 0x4
[08-11 16:49:48.515] [36964 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 65
[08-11 16:49:48.515] [36964 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:48.516] [36966 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:48.516] [36966 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:49:48.525] [37168 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 65
[08-11 16:49:48.525] [37168 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:48.525] [37168 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa9ff21a0 len 0x4
[08-11 16:49:48.525] [37168 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.525] [37168 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:48.525] [37168 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 65
[08-11 16:49:48.525] [37168 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:48.525] [37168 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.525] [37168 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.525] [37168 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.525] [37168 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.525] [37168 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:48.526] [37196 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 66
[08-11 16:49:48.526] [37196 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:49:48.526] [37196 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:48.526] [37196 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:48.526] [37196 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:48.526] [37196 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:49:48.526] [37196 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4c len 0x4
[08-11 16:49:48.526] [37196 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.526] [37196 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x4c len 0x4
[08-11 16:49:48.526] [37196 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 66
[08-11 16:49:48.526] [37196 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:48.526] [37198 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:48.526] [37198 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:49:48.535] [37400 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 66
[08-11 16:49:48.535] [37400 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:48.535] [37400 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa9ff3ea0 len 0x4
[08-11 16:49:48.535] [37400 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.535] [37400 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:48.535] [37400 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 66
[08-11 16:49:48.535] [37400 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:48.535] [37400 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.535] [37400 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.535] [37400 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.535] [37400 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.535] [37400 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:48.536] [37428 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 67
[08-11 16:49:48.536] [37428 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:49:48.536] [37428 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:48.536] [37428 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:48.536] [37428 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:48.536] [37428 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:49:48.536] [37428 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4d len 0x4
[08-11 16:49:48.536] [37428 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.536] [37428 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x4d len 0x4
[08-11 16:49:48.536] [37428 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 67
[08-11 16:49:48.536] [37428 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:48.536] [37430 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:48.536] [37430 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:49:48.545] [37632 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 67
[08-11 16:49:48.545] [37632 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:48.545] [37632 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa9ff5ba0 len 0x4
[08-11 16:49:48.545] [37632 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.545] [37632 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:48.545] [37632 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 67
[08-11 16:49:48.545] [37632 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:48.545] [37632 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.546] [37632 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.546] [37632 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.546] [37632 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.546] [37632 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:48.547] [37660 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 68
[08-11 16:49:48.547] [37660 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:49:48.547] [37660 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:48.547] [37660 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:48.547] [37660 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:48.547] [37660 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:49:48.547] [37660 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xd len 0x4
[08-11 16:49:48.547] [37660 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.547] [37660 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xd len 0x4
[08-11 16:49:48.547] [37660 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 68
[08-11 16:49:48.547] [37660 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:48.547] [37662 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:48.547] [37662 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:49:48.555] [37838 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 68
[08-11 16:49:48.555] [37838 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:48.555] [37838 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa9ff7560 len 0x4
[08-11 16:49:48.555] [37838 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.555] [37838 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:48.555] [37838 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 68
[08-11 16:49:48.555] [37838 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:48.555] [37838 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.555] [37838 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.555] [37838 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.555] [37838 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.555] [37838 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:48.556] [37866 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:48.556] [37866 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:48.556] [37866 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:48.556] [37866 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:48.556] [37866 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 69
[08-11 16:49:48.556] [37866 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:48.556] [37866 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xd len 0x4
[08-11 16:49:48.556] [37866 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.556] [37866 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xd len 0x4
[08-11 16:49:48.556] [37866 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 69
[08-11 16:49:48.556] [37866 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:48.556] [37868 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:48.556] [37868 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:48.560] [37944 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 69
[08-11 16:49:48.560] [37944 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:48.560] [37944 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa9ff82a0 len 0x4
[08-11 16:49:48.560] [37944 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.560] [37944 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:48.560] [37944 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 69
[08-11 16:49:48.560] [37944 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:48.560] [37944 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.560] [37944 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.560] [37944 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.560] [37944 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.560] [37944 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:48.561] [37972 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 70
[08-11 16:49:48.561] [37972 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:48.561] [37972 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:48.561] [37972 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:48.561] [37972 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:48.561] [37972 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:48.561] [37972 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa len 0x4
[08-11 16:49:48.561] [37972 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.561] [37972 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xa len 0x4
[08-11 16:49:48.561] [37972 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 70
[08-11 16:49:48.561] [37972 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:48.561] [37974 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:48.561] [37974 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:48.872] [44098 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 70
[08-11 16:49:48.872] [44098 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:48.872] [44098 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa014590 len 0x4
[08-11 16:49:48.872] [44098 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.872] [44098 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:48.872] [44098 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 70
[08-11 16:49:48.872] [44098 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:48.872] [44098 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.872] [44098 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.872] [44098 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.872] [44098 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.872] [44098 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:48.874] [44126 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:49:48.874] [44126 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:48.874] [44126 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:48.874] [44126 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:48.874] [44126 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 71
[08-11 16:49:48.874] [44126 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:49:48.874] [44126 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x43 len 0x4
[08-11 16:49:48.874] [44126 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.874] [44126 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x43 len 0x4
[08-11 16:49:48.874] [44126 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 71
[08-11 16:49:48.874] [44126 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:48.874] [44128 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:48.874] [44128 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:49:48.893] [44330 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 71
[08-11 16:49:48.893] [44330 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:48.893] [44330 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa016270 len 0x4
[08-11 16:49:48.893] [44330 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.893] [44330 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:48.893] [44330 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 71
[08-11 16:49:48.893] [44330 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:48.893] [44330 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.893] [44330 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.893] [44330 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.893] [44330 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.893] [44330 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:48.895] [44358 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:48.895] [44358 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:48.895] [44358 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:48.895] [44358 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:48.895] [44358 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 72
[08-11 16:49:48.895] [44358 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:48.895] [44358 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x50 len 0x4
[08-11 16:49:48.895] [44358 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.895] [44358 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x50 len 0x4
[08-11 16:49:48.895] [44358 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 72
[08-11 16:49:48.895] [44358 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:48.895] [44360 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:48.895] [44360 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:48.913] [44562 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 72
[08-11 16:49:48.913] [44562 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:48.913] [44562 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa017f70 len 0x4
[08-11 16:49:48.913] [44562 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.913] [44562 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:48.913] [44562 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 72
[08-11 16:49:48.913] [44562 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:48.913] [44562 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.913] [44562 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.913] [44562 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.913] [44562 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.913] [44562 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:48.916] [44590 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:49:48.916] [44590 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:48.916] [44590 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:48.916] [44590 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:48.916] [44590 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 73
[08-11 16:49:48.916] [44590 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:49:48.916] [44590 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x55 len 0x4
[08-11 16:49:48.916] [44590 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.916] [44590 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x55 len 0x4
[08-11 16:49:48.916] [44590 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 73
[08-11 16:49:48.916] [44590 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:48.916] [44592 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:48.916] [44592 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:49:48.934] [44794 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 73
[08-11 16:49:48.934] [44794 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:48.934] [44794 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa019c70 len 0x4
[08-11 16:49:48.934] [44794 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.934] [44794 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:48.934] [44794 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 73
[08-11 16:49:48.934] [44794 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:48.934] [44794 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.934] [44794 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.934] [44794 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.934] [44794 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.934] [44794 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:48.937] [44822 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:48.937] [44822 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:48.937] [44822 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:48.937] [44822 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:48.937] [44822 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 74
[08-11 16:49:48.937] [44822 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:48.937] [44822 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-11 16:49:48.937] [44822 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.937] [44822 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-11 16:49:48.937] [44822 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 74
[08-11 16:49:48.937] [44822 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:48.937] [44824 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:48.937] [44824 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:48.954] [45026 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 74
[08-11 16:49:48.954] [45026 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:48.954] [45026 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa01b970 len 0x4
[08-11 16:49:48.954] [45026 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.954] [45026 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:48.954] [45026 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 74
[08-11 16:49:48.954] [45026 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:48.954] [45026 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.954] [45026 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.954] [45026 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.954] [45026 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.954] [45026 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:48.957] [45054 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:49:48.957] [45054 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:48.957] [45054 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:48.957] [45054 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:48.957] [45054 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 75
[08-11 16:49:48.957] [45054 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:49:48.957] [45054 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x46 len 0x4
[08-11 16:49:48.957] [45054 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.957] [45054 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x46 len 0x4
[08-11 16:49:48.957] [45054 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 75
[08-11 16:49:48.957] [45054 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:48.957] [45056 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:48.957] [45056 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:49:48.975] [45258 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 75
[08-11 16:49:48.975] [45258 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:48.975] [45258 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa01d670 len 0x4
[08-11 16:49:48.975] [45258 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.975] [45258 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:48.975] [45258 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 75
[08-11 16:49:48.975] [45258 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:48.975] [45258 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.975] [45258 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.975] [45258 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.975] [45258 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.975] [45258 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:48.977] [45286 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:48.977] [45286 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:48.977] [45286 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:48.977] [45286 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:48.977] [45286 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 76
[08-11 16:49:48.977] [45286 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:48.977] [45286 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x72 len 0x4
[08-11 16:49:48.977] [45286 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.977] [45286 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x72 len 0x4
[08-11 16:49:48.977] [45286 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 76
[08-11 16:49:48.977] [45286 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:48.977] [45288 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:48.977] [45288 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:48.995] [45490 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 76
[08-11 16:49:48.995] [45490 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:48.995] [45490 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa02c780 len 0x4
[08-11 16:49:48.995] [45490 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.995] [45490 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:48.995] [45490 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 76
[08-11 16:49:48.995] [45490 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:48.995] [45490 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.995] [45490 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.995] [45490 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.995] [45490 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:48.995] [45490 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:48.997] [45518 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:49:48.997] [45518 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:48.997] [45518 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:48.997] [45518 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:48.997] [45518 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 77
[08-11 16:49:48.997] [45518 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:49:48.997] [45518 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x65 len 0x4
[08-11 16:49:48.997] [45518 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:48.997] [45518 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x65 len 0x4
[08-11 16:49:48.997] [45518 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 77
[08-11 16:49:48.997] [45518 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:48.997] [45520 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:48.997] [45520 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:49:49.014] [45722 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 77
[08-11 16:49:49.014] [45722 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:49.014] [45722 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa02e480 len 0x4
[08-11 16:49:49.014] [45722 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.014] [45722 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:49.014] [45722 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 77
[08-11 16:49:49.014] [45722 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:49.014] [45722 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.014] [45722 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.014] [45722 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.014] [45722 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.014] [45722 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:49.016] [45750 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:49.016] [45750 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:49.016] [45750 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:49.016] [45750 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:49.016] [45750 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 78
[08-11 16:49:49.016] [45750 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:49.016] [45750 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x71 len 0x4
[08-11 16:49:49.016] [45750 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.016] [45750 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x71 len 0x4
[08-11 16:49:49.016] [45750 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 78
[08-11 16:49:49.016] [45750 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:49.016] [45752 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:49.016] [45752 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:49.033] [45954 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 78
[08-11 16:49:49.033] [45954 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:49.033] [45954 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa030180 len 0x4
[08-11 16:49:49.033] [45954 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.033] [45954 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:49.033] [45954 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 78
[08-11 16:49:49.033] [45954 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:49.033] [45954 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.033] [45954 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.033] [45954 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.033] [45954 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.033] [45954 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:49.035] [45982 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:49:49.035] [45982 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:49.035] [45982 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:49.035] [45982 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:49.035] [45982 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 79
[08-11 16:49:49.035] [45982 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:49:49.035] [45982 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x75 len 0x4
[08-11 16:49:49.035] [45982 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.035] [45982 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x75 len 0x4
[08-11 16:49:49.035] [45982 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 79
[08-11 16:49:49.035] [45982 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:49.035] [45984 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:49.035] [45984 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:49:49.051] [46186 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 79
[08-11 16:49:49.051] [46186 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:49.051] [46186 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa031e80 len 0x4
[08-11 16:49:49.051] [46186 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.051] [46186 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:49.051] [46186 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 79
[08-11 16:49:49.051] [46186 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:49.051] [46186 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.051] [46186 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.051] [46186 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.051] [46186 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.051] [46186 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:49.053] [46214 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:49.053] [46214 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:49.053] [46214 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:49.053] [46214 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:49.053] [46214 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 80
[08-11 16:49:49.053] [46214 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:49.053] [46214 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x65 len 0x4
[08-11 16:49:49.053] [46214 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.053] [46214 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x65 len 0x4
[08-11 16:49:49.053] [46214 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 80
[08-11 16:49:49.053] [46214 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:49.054] [46216 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:49.054] [46216 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:49.069] [46418 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 80
[08-11 16:49:49.069] [46418 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:49.069] [46418 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa033b80 len 0x4
[08-11 16:49:49.069] [46418 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.069] [46418 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:49.069] [46418 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 80
[08-11 16:49:49.069] [46418 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:49.069] [46418 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.069] [46418 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.069] [46418 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.069] [46418 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.069] [46418 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:49.071] [46446 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:49:49.071] [46446 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:49.071] [46446 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:49.071] [46446 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:49.071] [46446 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 81
[08-11 16:49:49.071] [46446 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:49:49.071] [46446 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6e len 0x4
[08-11 16:49:49.071] [46446 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.071] [46446 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6e len 0x4
[08-11 16:49:49.071] [46446 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 81
[08-11 16:49:49.071] [46446 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:49.071] [46448 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:49.071] [46448 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:49:49.085] [46650 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 81
[08-11 16:49:49.085] [46650 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:49.085] [46650 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa035880 len 0x4
[08-11 16:49:49.085] [46650 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.085] [46650 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:49.085] [46650 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 81
[08-11 16:49:49.085] [46650 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:49.085] [46650 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.085] [46650 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.085] [46650 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.085] [46650 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.085] [46650 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:49.088] [46678 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:49.088] [46678 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:49.088] [46678 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:49.088] [46678 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:49.088] [46678 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 82
[08-11 16:49:49.088] [46678 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:49.088] [46678 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x63 len 0x4
[08-11 16:49:49.088] [46678 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.088] [46678 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x63 len 0x4
[08-11 16:49:49.088] [46678 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 82
[08-11 16:49:49.088] [46678 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:49.088] [46680 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:49.088] [46680 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:49.102] [46882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 82
[08-11 16:49:49.102] [46882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:49.102] [46882 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa037580 len 0x4
[08-11 16:49:49.102] [46882 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.102] [46882 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:49.102] [46882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 82
[08-11 16:49:49.102] [46882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:49.102] [46882 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.102] [46882 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.102] [46882 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.102] [46882 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.102] [46882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:49.104] [46910 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:49:49.104] [46910 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:49.104] [46910 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:49.104] [46910 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:49.104] [46910 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 83
[08-11 16:49:49.104] [46910 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:49:49.104] [46910 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x79 len 0x4
[08-11 16:49:49.104] [46910 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.104] [46910 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x79 len 0x4
[08-11 16:49:49.104] [46910 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 83
[08-11 16:49:49.104] [46910 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:49.105] [46912 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:49.105] [46912 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:49:49.119] [47114 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 83
[08-11 16:49:49.119] [47114 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:49.119] [47114 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa039280 len 0x4
[08-11 16:49:49.119] [47114 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.119] [47114 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:49.119] [47114 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 83
[08-11 16:49:49.119] [47114 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:49.119] [47114 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.119] [47114 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.119] [47114 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.119] [47114 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.119] [47114 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:49.121] [47142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:49.121] [47142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:49.121] [47142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:49.121] [47142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:49.121] [47142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 84
[08-11 16:49:49.121] [47142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:49.121] [47142 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-11 16:49:49.121] [47142 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.121] [47142 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-11 16:49:49.121] [47142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 84
[08-11 16:49:49.121] [47142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:49.121] [47144 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:49.122] [47144 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:49.136] [47346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 84
[08-11 16:49:49.136] [47346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:49.136] [47346 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa03af80 len 0x4
[08-11 16:49:49.136] [47346 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.136] [47346 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:49.136] [47346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 84
[08-11 16:49:49.136] [47346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:49.136] [47346 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.136] [47346 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.136] [47346 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.136] [47346 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.136] [47346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:49.138] [47374 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:49:49.138] [47374 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:49.138] [47374 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:49.138] [47374 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:49.138] [47374 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 85
[08-11 16:49:49.138] [47374 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:49:49.138] [47374 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x31 len 0x4
[08-11 16:49:49.138] [47374 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.138] [47374 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x31 len 0x4
[08-11 16:49:49.138] [47374 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 85
[08-11 16:49:49.138] [47374 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:49.138] [47376 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:49.138] [47376 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:49:49.153] [47578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 85
[08-11 16:49:49.153] [47578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:49.153] [47578 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa03cc80 len 0x4
[08-11 16:49:49.153] [47578 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.153] [47578 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:49.153] [47578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 85
[08-11 16:49:49.153] [47578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:49.153] [47578 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.153] [47578 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.153] [47578 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.153] [47578 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.153] [47578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:49.155] [47606 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:49.155] [47606 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:49.155] [47606 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:49.155] [47606 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:49.155] [47606 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 86
[08-11 16:49:49.155] [47606 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:49.155] [47606 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x36 len 0x4
[08-11 16:49:49.155] [47606 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.155] [47606 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x36 len 0x4
[08-11 16:49:49.155] [47606 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 86
[08-11 16:49:49.155] [47606 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:49.155] [47608 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:49.155] [47608 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:49.170] [47810 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 86
[08-11 16:49:49.170] [47810 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:49.170] [47810 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa03e980 len 0x4
[08-11 16:49:49.170] [47810 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.170] [47810 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:49.170] [47810 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 86
[08-11 16:49:49.170] [47810 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:49.170] [47810 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.170] [47810 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.170] [47810 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.170] [47810 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.170] [47810 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:49.172] [47838 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:49:49.172] [47838 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:49.172] [47838 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:49.172] [47838 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:49.172] [47838 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 87
[08-11 16:49:49.172] [47838 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:49:49.172] [47838 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x32 len 0x4
[08-11 16:49:49.172] [47838 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.172] [47838 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x32 len 0x4
[08-11 16:49:49.172] [47838 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 87
[08-11 16:49:49.172] [47838 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:49.172] [47840 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:49.172] [47840 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:49:49.187] [48042 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 87
[08-11 16:49:49.187] [48042 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:49.187] [48042 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa040680 len 0x4
[08-11 16:49:49.187] [48042 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.187] [48042 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:49.187] [48042 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 87
[08-11 16:49:49.187] [48042 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:49.187] [48042 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.187] [48042 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.187] [48042 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.187] [48042 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.187] [48042 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:49.189] [48070 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:49.189] [48070 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:49.189] [48070 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:49.189] [48070 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:49.189] [48070 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 88
[08-11 16:49:49.189] [48070 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:49.189] [48070 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x39 len 0x4
[08-11 16:49:49.189] [48070 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.189] [48070 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x39 len 0x4
[08-11 16:49:49.189] [48070 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 88
[08-11 16:49:49.189] [48070 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:49.189] [48072 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:49.189] [48072 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:49.204] [48274 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 88
[08-11 16:49:49.204] [48274 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:49.204] [48274 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa042380 len 0x4
[08-11 16:49:49.204] [48274 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.204] [48274 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:49.204] [48274 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 88
[08-11 16:49:49.204] [48274 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:49.204] [48274 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.204] [48274 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.204] [48274 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.204] [48274 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.204] [48274 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:49.206] [48302 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:49:49.206] [48302 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:49.206] [48302 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:49.206] [48302 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:49.206] [48302 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 89
[08-11 16:49:49.206] [48302 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:49:49.206] [48302 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x32 len 0x4
[08-11 16:49:49.206] [48302 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.206] [48302 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x32 len 0x4
[08-11 16:49:49.206] [48302 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 89
[08-11 16:49:49.206] [48302 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:49.206] [48304 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:49.206] [48304 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:49:49.221] [48506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 89
[08-11 16:49:49.221] [48506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:49.221] [48506 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa044080 len 0x4
[08-11 16:49:49.221] [48506 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.221] [48506 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:49.221] [48506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 89
[08-11 16:49:49.221] [48506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:49.221] [48506 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.221] [48506 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.221] [48506 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.221] [48506 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.221] [48506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:49.223] [48534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:49.223] [48534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:49.223] [48534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:49.223] [48534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:49.223] [48534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 90
[08-11 16:49:49.223] [48534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:49.223] [48534 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x31 len 0x4
[08-11 16:49:49.223] [48534 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.223] [48534 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x31 len 0x4
[08-11 16:49:49.223] [48534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 90
[08-11 16:49:49.223] [48534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:49.224] [48536 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:49.224] [48536 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:49.238] [48738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 90
[08-11 16:49:49.238] [48738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:49.238] [48738 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa045d80 len 0x4
[08-11 16:49:49.238] [48738 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.238] [48738 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:49.238] [48738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 90
[08-11 16:49:49.238] [48738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:49.238] [48738 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.238] [48738 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.238] [48738 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.238] [48738 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.238] [48738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:49.241] [48766 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:49:49.241] [48766 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:49.241] [48766 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:49.241] [48766 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:49.241] [48766 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 91
[08-11 16:49:49.241] [48766 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:49:49.241] [48766 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-11 16:49:49.241] [48766 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.241] [48766 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-11 16:49:49.241] [48766 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 91
[08-11 16:49:49.241] [48766 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:49.241] [48768 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:49.241] [48768 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:49:49.256] [48970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 91
[08-11 16:49:49.256] [48970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:49.256] [48970 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa047a80 len 0x4
[08-11 16:49:49.256] [48970 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.256] [48970 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:49.256] [48970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 91
[08-11 16:49:49.256] [48970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:49.256] [48970 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.256] [48970 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.256] [48970 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.256] [48970 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.256] [48970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:49.258] [48998 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:49.258] [48998 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:49.258] [48998 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:49.258] [48998 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:49.258] [48998 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 92
[08-11 16:49:49.258] [48998 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:49.258] [48998 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x48 len 0x4
[08-11 16:49:49.258] [48998 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.258] [48998 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x48 len 0x4
[08-11 16:49:49.258] [48998 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 92
[08-11 16:49:49.258] [48998 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:49.258] [49 us] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:49.258] [49 us] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:49.273] [49202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 92
[08-11 16:49:49.273] [49202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:49.273] [49202 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa049780 len 0x4
[08-11 16:49:49.273] [49202 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.273] [49202 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:49.273] [49202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 92
[08-11 16:49:49.273] [49202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:49.273] [49202 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.273] [49202 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.273] [49202 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.273] [49202 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.273] [49202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:49.275] [49230 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:49:49.275] [49230 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:49.275] [49230 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:49.275] [49230 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:49.275] [49230 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 93
[08-11 16:49:49.275] [49230 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:49:49.275] [49230 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x7a len 0x4
[08-11 16:49:49.275] [49230 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.275] [49230 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x7a len 0x4
[08-11 16:49:49.275] [49230 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 93
[08-11 16:49:49.275] [49230 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:49.275] [49232 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:49.275] [49232 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:49:49.290] [49434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 93
[08-11 16:49:49.290] [49434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:49.290] [49434 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa1222c0 len 0x4
[08-11 16:49:49.290] [49434 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.290] [49434 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:49.290] [49434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 93
[08-11 16:49:49.290] [49434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:49.290] [49434 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.290] [49434 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.290] [49434 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.290] [49434 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.290] [49434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:49.292] [49462 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:49.292] [49462 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:49.292] [49462 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:49.292] [49462 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:49.292] [49462 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 94
[08-11 16:49:49.292] [49462 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:49.292] [49462 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xd len 0x4
[08-11 16:49:49.292] [49462 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.292] [49462 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xd len 0x4
[08-11 16:49:49.292] [49462 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 94
[08-11 16:49:49.292] [49462 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:49.292] [49464 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:49.292] [49464 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:49.305] [49640 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 94
[08-11 16:49:49.305] [49640 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:49.305] [49640 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa123c80 len 0x4
[08-11 16:49:49.305] [49640 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.305] [49640 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:49.305] [49640 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 94
[08-11 16:49:49.305] [49640 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:49.305] [49640 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.305] [49640 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.305] [49640 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.305] [49640 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.305] [49640 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:49.307] [49668 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 95
[08-11 16:49:49.307] [49668 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:49.307] [49668 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:49.307] [49668 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:49.307] [49668 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:49.307] [49668 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:49.307] [49668 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xd len 0x4
[08-11 16:49:49.307] [49668 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.307] [49668 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xd len 0x4
[08-11 16:49:49.307] [49668 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 95
[08-11 16:49:49.307] [49668 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:49.313] [49670 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:49.313] [49670 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:49.319] [49746 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 95
[08-11 16:49:49.319] [49746 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:49.319] [49746 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa1249c0 len 0x4
[08-11 16:49:49.319] [49746 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.319] [49746 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:49.319] [49746 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 95
[08-11 16:49:49.319] [49746 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:49.319] [49746 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.319] [49746 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.319] [49746 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.319] [49746 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.319] [49746 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:49.321] [49774 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:49:49.321] [49774 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:49.321] [49774 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:49.321] [49774 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:49.321] [49774 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 96
[08-11 16:49:49.321] [49774 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:49:49.321] [49774 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa len 0x4
[08-11 16:49:49.321] [49774 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.321] [49774 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xa len 0x4
[08-11 16:49:49.321] [49774 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 96
[08-11 16:49:49.321] [49774 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:49.321] [49776 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:49.321] [49776 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:49:49.683] [54446 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 96
[08-11 16:49:49.683] [54446 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:49.683] [54446 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa3cbb90 len 0x4
[08-11 16:49:49.683] [54446 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.683] [54446 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:49.683] [54446 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 96
[08-11 16:49:49.683] [54446 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:49.683] [54446 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.683] [54446 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.683] [54446 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.683] [54446 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.683] [54446 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:49.685] [54474 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:49.685] [54474 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:49.685] [54474 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:49.685] [54474 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:49.685] [54474 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 97
[08-11 16:49:49.685] [54474 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:49.685] [54474 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x43 len 0x4
[08-11 16:49:49.685] [54474 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.685] [54474 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x43 len 0x4
[08-11 16:49:49.685] [54474 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 97
[08-11 16:49:49.685] [54474 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:49.685] [54476 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:49.685] [54476 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:49.700] [54678 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 97
[08-11 16:49:49.700] [54678 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:49.700] [54678 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa123660 len 0x4
[08-11 16:49:49.700] [54678 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.700] [54678 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:49.700] [54678 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 97
[08-11 16:49:49.700] [54678 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:49.700] [54678 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.700] [54678 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.700] [54678 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.700] [54678 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.700] [54678 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:49.703] [54706 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:49:49.703] [54706 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:49.703] [54706 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:49.703] [54706 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:49.703] [54706 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 98
[08-11 16:49:49.703] [54706 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:49:49.703] [54706 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x50 len 0x4
[08-11 16:49:49.703] [54706 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.703] [54706 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x50 len 0x4
[08-11 16:49:49.703] [54706 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 98
[08-11 16:49:49.703] [54706 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:49.703] [54708 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:49.703] [54708 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:49:49.718] [54910 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 98
[08-11 16:49:49.718] [54910 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:49.718] [54910 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa0293b0 len 0x4
[08-11 16:49:49.718] [54910 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.718] [54910 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:49.718] [54910 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 98
[08-11 16:49:49.718] [54910 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:49.718] [54910 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.718] [54910 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.718] [54910 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.718] [54910 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.718] [54910 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:49.720] [54938 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:49.720] [54938 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:49.720] [54938 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:49.720] [54938 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:49.720] [54938 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 99
[08-11 16:49:49.720] [54938 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:49.720] [54938 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x55 len 0x4
[08-11 16:49:49.720] [54938 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.720] [54938 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x55 len 0x4
[08-11 16:49:49.720] [54938 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 99
[08-11 16:49:49.720] [54938 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:49.720] [54940 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:49.720] [54940 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:49.735] [55142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 99
[08-11 16:49:49.735] [55142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:49.735] [55142 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa3c6fb0 len 0x4
[08-11 16:49:49.735] [55142 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.735] [55142 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:49.735] [55142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 99
[08-11 16:49:49.735] [55142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:49.735] [55142 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.735] [55142 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.735] [55142 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.735] [55142 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.735] [55142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:49.737] [55170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:49:49.737] [55170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:49.737] [55170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:49.737] [55170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:49.737] [55170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 100
[08-11 16:49:49.737] [55170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:49:49.737] [55170 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-11 16:49:49.737] [55170 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.737] [55170 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-11 16:49:49.737] [55170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 100
[08-11 16:49:49.737] [55170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:49.737] [55172 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:49.737] [55172 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:49:49.752] [55374 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 100
[08-11 16:49:49.752] [55374 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:49.752] [55374 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa3c8cb0 len 0x4
[08-11 16:49:49.752] [55374 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.752] [55374 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:49.752] [55374 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 100
[08-11 16:49:49.752] [55374 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:49.752] [55374 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.752] [55374 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.752] [55374 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.752] [55374 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.752] [55374 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:49.754] [55402 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:49.754] [55402 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:49.754] [55402 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:49.754] [55402 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:49.754] [55402 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 101
[08-11 16:49:49.754] [55402 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:49.754] [55402 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x48 len 0x4
[08-11 16:49:49.755] [55402 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.755] [55402 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x48 len 0x4
[08-11 16:49:49.755] [55402 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 101
[08-11 16:49:49.755] [55402 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:49.755] [55404 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:49.755] [55404 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:49.770] [55606 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 101
[08-11 16:49:49.770] [55606 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:49.770] [55606 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa9f9e7a0 len 0x4
[08-11 16:49:49.770] [55606 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.770] [55606 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:49.770] [55606 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 101
[08-11 16:49:49.770] [55606 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:49.770] [55606 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.770] [55606 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.770] [55606 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.770] [55606 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.770] [55606 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:49.772] [55634 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:49:49.772] [55634 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:49.772] [55634 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:49.772] [55634 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:49.772] [55634 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 102
[08-11 16:49:49.772] [55634 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:49:49.772] [55634 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x61 len 0x4
[08-11 16:49:49.772] [55634 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.772] [55634 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x61 len 0x4
[08-11 16:49:49.772] [55634 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 102
[08-11 16:49:49.772] [55634 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:49.772] [55636 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:49.772] [55636 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:49:49.788] [55838 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 102
[08-11 16:49:49.788] [55838 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:49.788] [55838 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa9fa04a0 len 0x4
[08-11 16:49:49.788] [55838 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.788] [55838 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:49.788] [55838 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 102
[08-11 16:49:49.788] [55838 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:49.788] [55838 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.788] [55838 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.788] [55838 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.788] [55838 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.788] [55838 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:49.790] [55866 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:49.790] [55866 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:49.790] [55866 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:49.790] [55866 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:49.790] [55866 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 103
[08-11 16:49:49.790] [55866 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:49.790] [55866 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x72 len 0x4
[08-11 16:49:49.790] [55866 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.790] [55866 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x72 len 0x4
[08-11 16:49:49.790] [55866 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 103
[08-11 16:49:49.790] [55866 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:49.790] [55868 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:49.790] [55868 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:49.805] [56070 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 103
[08-11 16:49:49.805] [56070 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:49.805] [56070 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa9f93ee0 len 0x4
[08-11 16:49:49.805] [56070 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.805] [56070 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:49.805] [56070 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 103
[08-11 16:49:49.805] [56070 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:49.805] [56070 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.805] [56070 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.805] [56070 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.805] [56070 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.805] [56070 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:49.807] [56098 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:49:49.807] [56098 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:49.807] [56098 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:49.807] [56098 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:49.807] [56098 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 104
[08-11 16:49:49.807] [56098 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:49:49.807] [56098 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x74 len 0x4
[08-11 16:49:49.807] [56098 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.807] [56098 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x74 len 0x4
[08-11 16:49:49.807] [56098 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 104
[08-11 16:49:49.807] [56098 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:49.807] [56100 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:49.807] [56100 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:49:49.822] [56302 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 104
[08-11 16:49:49.822] [56302 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:49.822] [56302 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa9f95be0 len 0x4
[08-11 16:49:49.822] [56302 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.822] [56302 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:49.822] [56302 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 104
[08-11 16:49:49.822] [56302 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:49.822] [56302 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.822] [56302 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.822] [56302 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.822] [56302 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.822] [56302 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:49.824] [56330 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:49.824] [56330 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:49.824] [56330 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:49.824] [56330 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:49.824] [56330 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 105
[08-11 16:49:49.824] [56330 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:49.824] [56330 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x49 len 0x4
[08-11 16:49:49.824] [56330 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.824] [56330 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x49 len 0x4
[08-11 16:49:49.824] [56330 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 105
[08-11 16:49:49.824] [56330 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:49.824] [56332 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:49.824] [56332 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:49.839] [56534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 105
[08-11 16:49:49.839] [56534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:49.839] [56534 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa9f978e0 len 0x4
[08-11 16:49:49.839] [56534 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.839] [56534 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:49.839] [56534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 105
[08-11 16:49:49.839] [56534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:49.839] [56534 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.839] [56534 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.839] [56534 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.839] [56534 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.839] [56534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:49.841] [56562 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:49:49.841] [56562 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:49.841] [56562 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:49.841] [56562 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:49.841] [56562 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 106
[08-11 16:49:49.841] [56562 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:49:49.841] [56562 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x44 len 0x4
[08-11 16:49:49.841] [56562 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.841] [56562 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x44 len 0x4
[08-11 16:49:49.841] [56562 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 106
[08-11 16:49:49.841] [56562 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:49.841] [56564 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:49.841] [56564 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:49:49.856] [56766 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 106
[08-11 16:49:49.856] [56766 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:49.856] [56766 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa9f99eb0 len 0x4
[08-11 16:49:49.856] [56766 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.856] [56766 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:49.856] [56766 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 106
[08-11 16:49:49.856] [56766 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:49.856] [56766 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.856] [56766 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.856] [56766 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.856] [56766 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.856] [56766 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:49.858] [56794 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:49.858] [56794 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:49.858] [56794 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:49.858] [56794 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:49.858] [56794 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 107
[08-11 16:49:49.858] [56794 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:49.858] [56794 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x3a len 0x4
[08-11 16:49:49.858] [56794 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.859] [56794 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x3a len 0x4
[08-11 16:49:49.859] [56794 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 107
[08-11 16:49:49.859] [56794 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:49.859] [56796 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:49.859] [56796 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:49.874] [56998 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 107
[08-11 16:49:49.874] [56998 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:49.874] [56998 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa9f9bbb0 len 0x4
[08-11 16:49:49.874] [56998 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.874] [56998 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:49.874] [56998 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 107
[08-11 16:49:49.874] [56998 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:49.874] [56998 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.874] [56998 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.874] [56998 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.874] [56998 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.874] [56998 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:49.876] [57026 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:49:49.876] [57026 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:49.876] [57026 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:49.876] [57026 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:49.876] [57026 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 108
[08-11 16:49:49.876] [57026 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:49:49.876] [57026 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-11 16:49:49.876] [57026 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.876] [57026 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-11 16:49:49.876] [57026 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 108
[08-11 16:49:49.876] [57026 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:49.876] [57028 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:49.876] [57028 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:49:49.892] [57230 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 108
[08-11 16:49:49.892] [57230 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:49.892] [57230 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa0bd950 len 0x4
[08-11 16:49:49.892] [57230 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.892] [57230 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:49.892] [57230 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 108
[08-11 16:49:49.892] [57230 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:49.892] [57230 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.892] [57230 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.892] [57230 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.892] [57230 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.892] [57230 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:49.894] [57258 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:49.894] [57258 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:49.894] [57258 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:49.894] [57258 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:49.894] [57258 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 109
[08-11 16:49:49.894] [57258 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:49.894] [57258 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x30 len 0x4
[08-11 16:49:49.894] [57258 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.894] [57258 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x30 len 0x4
[08-11 16:49:49.894] [57258 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 109
[08-11 16:49:49.894] [57258 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:49.894] [57260 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:49.894] [57260 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:49:49.909] [57462 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 109
[08-11 16:49:49.909] [57462 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:49.909] [57462 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa0bf650 len 0x4
[08-11 16:49:49.909] [57462 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.909] [57462 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:49.909] [57462 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 109
[08-11 16:49:49.909] [57462 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:49.909] [57462 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.909] [57462 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.909] [57462 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.909] [57462 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.909] [57462 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:49.911] [57490 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:49:49.911] [57490 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:49.911] [57490 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:49.911] [57490 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:49.911] [57490 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 110
[08-11 16:49:49.911] [57490 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:49:49.911] [57490 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xd len 0x4
[08-11 16:49:49.911] [57490 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.911] [57490 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xd len 0x4
[08-11 16:49:49.911] [57490 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 110
[08-11 16:49:49.911] [57490 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:49.911] [57492 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:49.911] [57492 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:49:49.924] [57668 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 110
[08-11 16:49:49.924] [57668 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:49.924] [57668 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa0c1010 len 0x4
[08-11 16:49:49.924] [57668 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.924] [57668 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:49.924] [57668 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 110
[08-11 16:49:49.924] [57668 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:49.924] [57668 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.924] [57668 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.924] [57668 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.924] [57668 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.924] [57668 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:49.926] [57696 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 111
[08-11 16:49:49.926] [57696 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:49:49.926] [57696 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:49.926] [57696 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:49.926] [57696 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:49.926] [57696 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:49:49.926] [57696 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xd len 0x4
[08-11 16:49:49.926] [57696 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.926] [57696 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xd len 0x4
[08-11 16:49:49.926] [57696 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 111
[08-11 16:49:49.926] [57696 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:49.926] [57698 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:49.926] [57698 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:49:49.932] [57774 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 111
[08-11 16:49:49.932] [57774 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:49:49.932] [57774 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa0c1d50 len 0x4
[08-11 16:49:49.932] [57774 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.932] [57774 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:49:49.932] [57774 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 111
[08-11 16:49:49.932] [57774 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:49:49.932] [57774 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.932] [57774 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.932] [57774 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.932] [57774 ns] axi2tlm: rdata.write 0x0
[08-11 16:49:49.932] [57774 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:49:49.934] [57802 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:49:49.934] [57802 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:49:49.934] [57802 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:49:49.934] [57802 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:49:49.934] [57802 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 112
[08-11 16:49:49.934] [57802 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:49:49.934] [57802 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa len 0x4
[08-11 16:49:49.934] [57802 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:49:49.934] [57802 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xa len 0x4
[08-11 16:49:49.934] [57802 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 112
[08-11 16:49:49.934] [57802 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:49:49.934] [57804 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:49:49.934] [57804 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:50:52.528] [734874 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 112
[08-11 16:50:52.528] [734874 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:50:52.528] [734874 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa9f69890 len 0x4
[08-11 16:50:52.528] [734874 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.528] [734874 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:50:52.528] [734874 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 112
[08-11 16:50:52.528] [734874 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:50:52.528] [734874 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.528] [734874 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.528] [734874 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.528] [734874 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.528] [734874 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:50:52.531] [734902 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:50:52.531] [734902 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:50:52.531] [734902 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:50:52.531] [734902 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:50:52.531] [734902 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 113
[08-11 16:50:52.531] [734902 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:50:52.531] [734902 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x41 len 0x4
[08-11 16:50:52.531] [734902 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.531] [734902 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x41 len 0x4
[08-11 16:50:52.531] [734902 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 113
[08-11 16:50:52.531] [734902 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:50:52.531] [734904 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:50:52.531] [734904 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:50:52.543] [735092 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 113
[08-11 16:50:52.543] [735092 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:50:52.543] [735092 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa9f5d7f0 len 0x4
[08-11 16:50:52.543] [735092 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.543] [735092 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:50:52.543] [735092 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 113
[08-11 16:50:52.543] [735092 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:50:52.543] [735092 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.543] [735092 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.543] [735092 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.543] [735092 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.543] [735092 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:50:52.544] [735120 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 114
[08-11 16:50:52.544] [735120 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:50:52.544] [735120 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:50:52.544] [735120 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:50:52.544] [735120 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:50:52.544] [735120 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:50:52.544] [735120 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6c len 0x4
[08-11 16:50:52.544] [735120 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.544] [735120 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6c len 0x4
[08-11 16:50:52.544] [735120 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 114
[08-11 16:50:52.544] [735120 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:50:52.544] [735122 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:50:52.544] [735122 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:50:52.551] [735272 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 114
[08-11 16:50:52.551] [735272 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:50:52.551] [735272 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa9f5ee70 len 0x4
[08-11 16:50:52.551] [735272 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.551] [735272 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:50:52.551] [735272 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 114
[08-11 16:50:52.551] [735272 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:50:52.551] [735272 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.551] [735272 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.551] [735272 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.551] [735272 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.551] [735272 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:50:52.553] [735300 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 115
[08-11 16:50:52.553] [735300 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:50:52.553] [735300 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:50:52.553] [735300 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:50:52.553] [735300 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:50:52.553] [735300 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:50:52.553] [735300 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6c len 0x4
[08-11 16:50:52.553] [735300 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.553] [735300 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6c len 0x4
[08-11 16:50:52.553] [735300 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 115
[08-11 16:50:52.553] [735300 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:50:52.553] [735302 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:50:52.553] [735302 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:50:52.558] [735414 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 115
[08-11 16:50:52.558] [735414 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:50:52.558] [735414 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa9f6a910 len 0x4
[08-11 16:50:52.558] [735414 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.558] [735414 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:50:52.558] [735414 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 115
[08-11 16:50:52.558] [735414 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:50:52.558] [735414 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.558] [735414 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.558] [735414 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.558] [735414 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.558] [735414 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:50:52.559] [735442 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:50:52.559] [735442 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:50:52.559] [735442 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:50:52.559] [735442 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:50:52.559] [735442 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 116
[08-11 16:50:52.559] [735442 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:50:52.559] [735442 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-11 16:50:52.559] [735442 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.559] [735442 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-11 16:50:52.559] [735442 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 116
[08-11 16:50:52.559] [735442 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:50:52.559] [735444 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:50:52.559] [735444 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:50:52.564] [735556 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 116
[08-11 16:50:52.564] [735556 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:50:52.564] [735556 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa9f6bad0 len 0x4
[08-11 16:50:52.564] [735556 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.564] [735556 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:50:52.564] [735556 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 116
[08-11 16:50:52.564] [735556 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:50:52.564] [735556 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.564] [735556 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.564] [735556 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.564] [735556 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.564] [735556 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:50:52.566] [735584 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 117
[08-11 16:50:52.566] [735584 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:50:52.566] [735584 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:50:52.566] [735584 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:50:52.566] [735584 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:50:52.566] [735584 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:50:52.566] [735584 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x74 len 0x4
[08-11 16:50:52.566] [735584 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.566] [735584 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x74 len 0x4
[08-11 16:50:52.566] [735584 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 117
[08-11 16:50:52.566] [735584 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:50:52.566] [735586 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:50:52.566] [735586 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:50:52.571] [735698 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 117
[08-11 16:50:52.571] [735698 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:50:52.571] [735698 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa9f6cc90 len 0x4
[08-11 16:50:52.571] [735698 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.571] [735698 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:50:52.571] [735698 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 117
[08-11 16:50:52.571] [735698 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:50:52.571] [735698 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.571] [735698 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.571] [735698 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.571] [735698 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.571] [735698 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:50:52.572] [735726 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:50:52.572] [735726 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:50:52.572] [735726 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:50:52.572] [735726 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:50:52.572] [735726 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 118
[08-11 16:50:52.572] [735726 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:50:52.572] [735726 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x65 len 0x4
[08-11 16:50:52.572] [735726 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.572] [735726 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x65 len 0x4
[08-11 16:50:52.572] [735726 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 118
[08-11 16:50:52.572] [735726 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:50:52.573] [735728 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:50:52.573] [735728 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:50:52.578] [735840 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 118
[08-11 16:50:52.578] [735840 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:50:52.578] [735840 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa9f6de50 len 0x4
[08-11 16:50:52.578] [735840 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.578] [735840 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:50:52.578] [735840 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 118
[08-11 16:50:52.578] [735840 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:50:52.578] [735840 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.578] [735840 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.578] [735840 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.578] [735840 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.578] [735840 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:50:52.579] [735868 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 119
[08-11 16:50:52.579] [735868 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:50:52.579] [735868 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:50:52.579] [735868 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:50:52.579] [735868 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:50:52.579] [735868 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:50:52.579] [735868 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x73 len 0x4
[08-11 16:50:52.579] [735868 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.579] [735868 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x73 len 0x4
[08-11 16:50:52.579] [735868 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 119
[08-11 16:50:52.579] [735868 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:50:52.579] [735870 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:50:52.579] [735870 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:50:52.584] [735982 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 119
[08-11 16:50:52.584] [735982 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:50:52.584] [735982 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa80000a0 len 0x4
[08-11 16:50:52.584] [735982 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.584] [735982 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:50:52.584] [735982 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 119
[08-11 16:50:52.584] [735982 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:50:52.584] [735982 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.584] [735982 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.584] [735982 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.584] [735982 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.584] [735982 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:50:52.586] [736010 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:50:52.586] [736010 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:50:52.586] [736010 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:50:52.586] [736010 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:50:52.586] [736010 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 120
[08-11 16:50:52.586] [736010 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:50:52.586] [736010 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x74 len 0x4
[08-11 16:50:52.586] [736010 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.586] [736010 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x74 len 0x4
[08-11 16:50:52.586] [736010 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 120
[08-11 16:50:52.586] [736010 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:50:52.586] [736012 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:50:52.586] [736012 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:50:52.591] [736124 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 120
[08-11 16:50:52.591] [736124 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:50:52.591] [736124 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa9f6fdb0 len 0x4
[08-11 16:50:52.591] [736124 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.591] [736124 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:50:52.591] [736124 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 120
[08-11 16:50:52.591] [736124 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:50:52.591] [736124 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.591] [736124 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.591] [736124 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.591] [736124 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.591] [736124 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:50:52.592] [736152 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 121
[08-11 16:50:52.592] [736152 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:50:52.592] [736152 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:50:52.592] [736152 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:50:52.592] [736152 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:50:52.592] [736152 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:50:52.592] [736152 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x73 len 0x4
[08-11 16:50:52.592] [736152 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.592] [736152 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x73 len 0x4
[08-11 16:50:52.592] [736152 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 121
[08-11 16:50:52.592] [736152 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:50:52.592] [736154 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:50:52.592] [736154 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:50:52.597] [736266 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 121
[08-11 16:50:52.597] [736266 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:50:52.597] [736266 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa9f3a480 len 0x4
[08-11 16:50:52.597] [736266 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.597] [736266 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:50:52.597] [736266 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 121
[08-11 16:50:52.597] [736266 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:50:52.597] [736266 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.597] [736266 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.597] [736266 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.597] [736266 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.597] [736266 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:50:52.599] [736294 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:50:52.599] [736294 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:50:52.599] [736294 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:50:52.599] [736294 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:50:52.599] [736294 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 122
[08-11 16:50:52.599] [736294 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:50:52.599] [736294 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[08-11 16:50:52.599] [736294 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.599] [736294 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[08-11 16:50:52.599] [736294 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 122
[08-11 16:50:52.599] [736294 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:50:52.599] [736296 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:50:52.599] [736296 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:50:52.604] [736408 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 122
[08-11 16:50:52.604] [736408 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:50:52.604] [736408 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa9f3b640 len 0x4
[08-11 16:50:52.604] [736408 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.604] [736408 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:50:52.604] [736408 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 122
[08-11 16:50:52.604] [736408 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:50:52.604] [736408 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.604] [736408 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.604] [736408 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.604] [736408 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.604] [736408 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:50:52.605] [736436 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 123
[08-11 16:50:52.605] [736436 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:50:52.605] [736436 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:50:52.605] [736436 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:50:52.605] [736436 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:50:52.605] [736436 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:50:52.605] [736436 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x66 len 0x4
[08-11 16:50:52.605] [736436 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.605] [736436 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x66 len 0x4
[08-11 16:50:52.605] [736436 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 123
[08-11 16:50:52.605] [736436 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:50:52.605] [736438 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:50:52.605] [736438 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:50:52.610] [736550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 123
[08-11 16:50:52.610] [736550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:50:52.610] [736550 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa9f3c800 len 0x4
[08-11 16:50:52.610] [736550 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.610] [736550 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:50:52.610] [736550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 123
[08-11 16:50:52.610] [736550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:50:52.610] [736550 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.610] [736550 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.610] [736550 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.610] [736550 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.610] [736550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:50:52.612] [736578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:50:52.612] [736578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:50:52.612] [736578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:50:52.612] [736578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:50:52.612] [736578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 124
[08-11 16:50:52.612] [736578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:50:52.612] [736578 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x69 len 0x4
[08-11 16:50:52.612] [736578 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.612] [736578 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x69 len 0x4
[08-11 16:50:52.612] [736578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 124
[08-11 16:50:52.612] [736578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:50:52.612] [736580 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:50:52.612] [736580 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:50:52.627] [736692 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 124
[08-11 16:50:52.627] [736692 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:50:52.627] [736692 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa9f5f890 len 0x4
[08-11 16:50:52.627] [736692 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.627] [736692 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:50:52.627] [736692 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 124
[08-11 16:50:52.627] [736692 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:50:52.627] [736692 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.627] [736692 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.627] [736692 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.627] [736692 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.627] [736692 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:50:52.628] [736720 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 125
[08-11 16:50:52.628] [736720 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:50:52.628] [736720 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:50:52.628] [736720 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:50:52.628] [736720 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:50:52.628] [736720 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:50:52.628] [736720 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6e len 0x4
[08-11 16:50:52.628] [736720 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.628] [736720 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6e len 0x4
[08-11 16:50:52.628] [736720 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 125
[08-11 16:50:52.628] [736720 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:50:52.628] [736722 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:50:52.628] [736722 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:50:52.634] [736834 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 125
[08-11 16:50:52.634] [736834 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:50:52.634] [736834 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa057db0 len 0x4
[08-11 16:50:52.634] [736834 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.634] [736834 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:50:52.634] [736834 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 125
[08-11 16:50:52.634] [736834 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:50:52.634] [736834 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.634] [736834 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.634] [736834 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.634] [736834 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.634] [736834 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:50:52.636] [736862 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:50:52.636] [736862 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:50:52.636] [736862 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:50:52.636] [736862 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:50:52.636] [736862 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 126
[08-11 16:50:52.636] [736862 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:50:52.636] [736862 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x69 len 0x4
[08-11 16:50:52.636] [736862 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.636] [736862 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x69 len 0x4
[08-11 16:50:52.636] [736862 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 126
[08-11 16:50:52.636] [736862 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:50:52.636] [736864 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:50:52.636] [736864 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:50:52.641] [736976 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 126
[08-11 16:50:52.641] [736976 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:50:52.641] [736976 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa0c68f0 len 0x4
[08-11 16:50:52.641] [736976 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.641] [736976 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:50:52.641] [736976 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 126
[08-11 16:50:52.641] [736976 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:50:52.641] [736976 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.641] [736976 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.641] [736976 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.641] [736976 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.641] [736976 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:50:52.642] [737004 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 127
[08-11 16:50:52.642] [737004 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:50:52.642] [737004 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:50:52.642] [737004 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:50:52.642] [737004 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:50:52.642] [737004 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:50:52.642] [737004 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x73 len 0x4
[08-11 16:50:52.642] [737004 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.642] [737004 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x73 len 0x4
[08-11 16:50:52.642] [737004 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 127
[08-11 16:50:52.642] [737004 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:50:52.642] [737006 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:50:52.642] [737006 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:50:52.648] [737118 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 127
[08-11 16:50:52.648] [737118 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:50:52.648] [737118 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa062f50 len 0x4
[08-11 16:50:52.648] [737118 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.648] [737118 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:50:52.648] [737118 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 127
[08-11 16:50:52.648] [737118 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:50:52.648] [737118 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.648] [737118 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.648] [737118 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.648] [737118 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.648] [737118 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:50:52.649] [737146 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:50:52.649] [737146 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:50:52.649] [737146 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:50:52.649] [737146 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:50:52.649] [737146 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 128
[08-11 16:50:52.649] [737146 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:50:52.649] [737146 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x68 len 0x4
[08-11 16:50:52.649] [737146 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.649] [737146 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x68 len 0x4
[08-11 16:50:52.649] [737146 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 128
[08-11 16:50:52.649] [737146 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:50:52.649] [737148 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:50:52.649] [737148 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:50:52.654] [737260 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 128
[08-11 16:50:52.654] [737260 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:50:52.654] [737260 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa026b20 len 0x4
[08-11 16:50:52.654] [737260 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.654] [737260 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:50:52.654] [737260 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 128
[08-11 16:50:52.654] [737260 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:50:52.654] [737260 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.654] [737260 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.654] [737260 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.654] [737260 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.654] [737260 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:50:52.655] [737288 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 129
[08-11 16:50:52.655] [737288 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:50:52.655] [737288 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:50:52.655] [737288 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:50:52.655] [737288 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:50:52.655] [737288 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:50:52.655] [737288 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x65 len 0x4
[08-11 16:50:52.655] [737288 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.655] [737288 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x65 len 0x4
[08-11 16:50:52.655] [737288 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 129
[08-11 16:50:52.655] [737288 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:50:52.656] [737290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:50:52.656] [737290 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:50:52.661] [737402 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 129
[08-11 16:50:52.661] [737402 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:50:52.661] [737402 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa036640 len 0x4
[08-11 16:50:52.661] [737402 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.661] [737402 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:50:52.661] [737402 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 129
[08-11 16:50:52.661] [737402 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:50:52.661] [737402 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.661] [737402 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.661] [737402 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.661] [737402 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.661] [737402 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:50:52.662] [737430 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:50:52.662] [737430 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:50:52.662] [737430 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:50:52.662] [737430 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:50:52.662] [737430 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 130
[08-11 16:50:52.662] [737430 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:50:52.662] [737430 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x64 len 0x4
[08-11 16:50:52.662] [737430 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.662] [737430 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x64 len 0x4
[08-11 16:50:52.662] [737430 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 130
[08-11 16:50:52.662] [737430 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:50:52.662] [737432 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:50:52.662] [737432 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:50:52.668] [737560 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 130
[08-11 16:50:52.668] [737560 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:50:52.668] [737560 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa13b1d0 len 0x4
[08-11 16:50:52.668] [737560 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.668] [737560 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:50:52.668] [737560 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 130
[08-11 16:50:52.668] [737560 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:50:52.668] [737560 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.668] [737560 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.668] [737560 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.668] [737560 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.668] [737560 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:50:52.669] [737588 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 131
[08-11 16:50:52.669] [737588 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:50:52.669] [737588 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:50:52.669] [737588 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:50:52.669] [737588 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:50:52.669] [737588 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:50:52.669] [737588 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xd len 0x4
[08-11 16:50:52.669] [737588 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.669] [737588 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xd len 0x4
[08-11 16:50:52.669] [737588 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 131
[08-11 16:50:52.669] [737588 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:50:52.669] [737590 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:50:52.669] [737590 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:50:52.672] [737642 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 131
[08-11 16:50:52.672] [737642 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:50:52.672] [737642 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa0b4010 len 0x4
[08-11 16:50:52.672] [737642 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.672] [737642 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:50:52.672] [737642 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 131
[08-11 16:50:52.672] [737642 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:50:52.672] [737642 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.672] [737642 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.672] [737642 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.672] [737642 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.672] [737642 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:50:52.673] [737670 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:50:52.673] [737670 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:50:52.673] [737670 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:50:52.673] [737670 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:50:52.673] [737670 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 132
[08-11 16:50:52.673] [737670 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:50:52.673] [737670 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa len 0x4
[08-11 16:50:52.673] [737670 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.673] [737670 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xa len 0x4
[08-11 16:50:52.673] [737670 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 132
[08-11 16:50:52.673] [737670 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:50:52.673] [737672 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:50:52.673] [737672 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:50:52.693] [738110 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 132
[08-11 16:50:52.693] [738110 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:50:52.693] [738110 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa9fd4310 len 0x4
[08-11 16:50:52.693] [738110 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.693] [738110 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:50:52.693] [738110 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 132
[08-11 16:50:52.693] [738110 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:50:52.693] [738110 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.693] [738110 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.693] [738110 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.693] [738110 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.693] [738110 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:50:52.694] [738138 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:50:52.694] [738138 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:50:52.694] [738138 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:50:52.694] [738138 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:50:52.694] [738138 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 133
[08-11 16:50:52.694] [738138 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:50:52.694] [738138 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x0 len 0x4
[08-11 16:50:52.694] [738138 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.694] [738138 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:50:52.694] [738138 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 133
[08-11 16:50:52.694] [738138 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:50:52.695] [738140 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:50:52.695] [738140 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:50:52.698] [738224 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 133
[08-11 16:50:52.698] [738224 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:50:52.698] [738224 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa141b30 len 0x4
[08-11 16:50:52.698] [738224 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.698] [738224 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:50:52.698] [738224 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 133
[08-11 16:50:52.698] [738224 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:50:52.698] [738224 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.698] [738224 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.698] [738224 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.698] [738224 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.698] [738224 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:50:52.700] [738252 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 134
[08-11 16:50:52.700] [738252 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:50:52.700] [738252 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:50:52.700] [738252 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:50:52.700] [738252 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:50:52.700] [738252 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:50:52.700] [738252 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x0 len 0x4
[08-11 16:50:52.700] [738252 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.700] [738252 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:50:52.700] [738252 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 134
[08-11 16:50:52.700] [738252 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:50:52.700] [738254 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:50:52.700] [738254 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:50:52.703] [738318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 134
[08-11 16:50:52.703] [738318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:50:52.703] [738318 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa9ff6f70 len 0x4
[08-11 16:50:52.703] [738318 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.703] [738318 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:50:52.703] [738318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 134
[08-11 16:50:52.703] [738318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:50:52.703] [738318 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.703] [738318 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.703] [738318 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.703] [738318 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.703] [738318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:50:52.704] [738346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:50:52.704] [738346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:50:52.704] [738346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:50:52.704] [738346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:50:52.704] [738346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 135
[08-11 16:50:52.704] [738346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:50:52.704] [738346 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x0 len 0x4
[08-11 16:50:52.704] [738346 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.704] [738346 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:50:52.704] [738346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 135
[08-11 16:50:52.704] [738346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:50:52.704] [738348 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:50:52.704] [738348 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:50:52.707] [738412 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 135
[08-11 16:50:52.707] [738412 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:50:52.707] [738412 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa15ad40 len 0x4
[08-11 16:50:52.707] [738412 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.707] [738412 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:50:52.707] [738412 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 135
[08-11 16:50:52.707] [738412 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:50:52.707] [738412 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.707] [738412 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.707] [738412 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.707] [738412 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.707] [738412 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:50:52.708] [738440 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 136
[08-11 16:50:52.708] [738440 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:50:52.708] [738440 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:50:52.708] [738440 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:50:52.708] [738440 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:50:52.708] [738440 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:50:52.708] [738440 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x0 len 0x4
[08-11 16:50:52.708] [738440 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.708] [738440 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:50:52.708] [738440 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 136
[08-11 16:50:52.708] [738440 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:50:52.708] [738442 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:50:52.708] [738442 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:50:52.711] [738506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 136
[08-11 16:50:52.711] [738506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:50:52.711] [738506 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa144bb0 len 0x4
[08-11 16:50:52.711] [738506 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.711] [738506 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:50:52.711] [738506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 136
[08-11 16:50:52.711] [738506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:50:52.711] [738506 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.711] [738506 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.711] [738506 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.711] [738506 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.711] [738506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:50:52.712] [738534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:50:52.712] [738534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:50:52.712] [738534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:50:52.712] [738534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:50:52.712] [738534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 137
[08-11 16:50:52.712] [738534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:50:52.712] [738534 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x0 len 0x4
[08-11 16:50:52.712] [738534 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.712] [738534 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:50:52.712] [738534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 137
[08-11 16:50:52.712] [738534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:50:52.713] [738536 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:50:52.713] [738536 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:50:52.715] [738600 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 137
[08-11 16:50:52.715] [738600 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:50:52.715] [738600 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa12c5d0 len 0x4
[08-11 16:50:52.715] [738600 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.715] [738600 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:50:52.715] [738600 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 137
[08-11 16:50:52.715] [738600 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:50:52.715] [738600 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.715] [738600 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.715] [738600 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.715] [738600 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.715] [738600 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:50:52.717] [738628 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 138
[08-11 16:50:52.717] [738628 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:50:52.717] [738628 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:50:52.717] [738628 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:50:52.717] [738628 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:50:52.717] [738628 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:50:52.717] [738628 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x0 len 0x4
[08-11 16:50:52.717] [738628 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.717] [738628 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:50:52.717] [738628 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 138
[08-11 16:50:52.717] [738628 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:50:52.717] [738630 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:50:52.717] [738630 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:50:52.720] [738694 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 138
[08-11 16:50:52.720] [738694 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:50:52.720] [738694 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa153340 len 0x4
[08-11 16:50:52.720] [738694 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.720] [738694 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:50:52.720] [738694 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 138
[08-11 16:50:52.720] [738694 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:50:52.720] [738694 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.720] [738694 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.720] [738694 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.720] [738694 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.720] [738694 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:50:52.721] [738722 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:50:52.721] [738722 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:50:52.721] [738722 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:50:52.721] [738722 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:50:52.721] [738722 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 139
[08-11 16:50:52.721] [738722 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:50:52.721] [738722 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x0 len 0x4
[08-11 16:50:52.721] [738722 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.721] [738722 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:50:52.721] [738722 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 139
[08-11 16:50:52.721] [738722 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:50:52.721] [738724 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:50:52.721] [738724 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:50:52.724] [738788 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 139
[08-11 16:50:52.724] [738788 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:50:52.724] [738788 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa0350b0 len 0x4
[08-11 16:50:52.724] [738788 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.724] [738788 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:50:52.724] [738788 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 139
[08-11 16:50:52.724] [738788 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:50:52.724] [738788 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.724] [738788 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.724] [738788 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.724] [738788 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.724] [738788 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:50:52.725] [738816 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 140
[08-11 16:50:52.725] [738816 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:50:52.725] [738816 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:50:52.725] [738816 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:50:52.725] [738816 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:50:52.725] [738816 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:50:52.725] [738816 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x0 len 0x4
[08-11 16:50:52.725] [738816 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.725] [738816 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:50:52.725] [738816 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 140
[08-11 16:50:52.725] [738816 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:50:52.725] [738818 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:50:52.725] [738818 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
[08-11 16:50:52.728] [738882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1aa4402b0, reqid: 140
[08-11 16:50:52.728] [738882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:50:52.728] [738882 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa0c4890 len 0x4
[08-11 16:50:52.728] [738882 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.728] [738882 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:50:52.728] [738882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 140
[08-11 16:50:52.728] [738882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:50:52.728] [738882 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.728] [738882 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.728] [738882 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.728] [738882 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.728] [738882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:50:52.730] [738910 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:50:52.730] [738910 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:50:52.730] [738910 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:50:52.730] [738910 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:50:52.730] [738910 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 141
[08-11 16:50:52.730] [738910 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:50:52.730] [738910 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x0 len 0x4
[08-11 16:50:52.730] [738910 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.730] [738910 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:50:52.730] [738910 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 141
[08-11 16:50:52.730] [738910 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:50:52.730] [738912 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:50:52.730] [738912 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:50:52.733] [738976 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 141
[08-11 16:50:52.733] [738976 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:50:52.733] [738976 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xaa0cff90 len 0x4
[08-11 16:50:52.733] [738976 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.733] [738976 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:50:52.733] [738976 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 141
[08-11 16:50:52.733] [738976 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:50:52.733] [738976 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.733] [738976 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.733] [738976 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.733] [738976 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.733] [738976 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:50:52.734] [739004 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1a8173ed0, reqid: 142
[08-11 16:50:52.734] [739004 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1aa4402b0
[08-11 16:50:52.734] [739004 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:50:52.734] [739004 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:50:52.734] [739004 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:50:52.734] [739004 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1a8173ed0, wt: 0x7fc1aa4402b0
[08-11 16:50:52.734] [739004 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x0 len 0x4
[08-11 16:50:52.734] [739004 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.734] [739004 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:50:52.734] [739004 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 142
[08-11 16:50:52.734] [739004 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:50:52.734] [739006 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:50:52.734] [739006 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1a8173ed0
[08-11 16:50:52.738] [739090 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7fc1a8173ed0, reqid: 142
[08-11 16:50:52.738] [739090 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[08-11 16:50:52.738] [739090 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa9fd17a0 len 0x4
[08-11 16:50:52.738] [739090 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.738] [739090 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[08-11 16:50:52.738] [739090 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 142
[08-11 16:50:52.738] [739090 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[08-11 16:50:52.738] [739090 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.738] [739090 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.738] [739090 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.738] [739090 ns] axi2tlm: rdata.write 0x0
[08-11 16:50:52.738] [739090 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[08-11 16:50:52.739] [739118 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7fc1a8173ed0
[08-11 16:50:52.739] [739118 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[08-11 16:50:52.739] [739118 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[08-11 16:50:52.739] [739118 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[08-11 16:50:52.739] [739118 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7fc1aa4402b0, reqid: 143
[08-11 16:50:52.739] [739118 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7fc1aa4402b0, wt: 0x7fc1a8173ed0
[08-11 16:50:52.739] [739118 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa len 0x4
[08-11 16:50:52.739] [739118 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[08-11 16:50:52.739] [739118 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xa len 0x4
[08-11 16:50:52.739] [739118 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 143
[08-11 16:50:52.739] [739118 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[08-11 16:50:52.739] [739120 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[08-11 16:50:52.739] [739120 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7fc1aa4402b0
