[08-11 16:33:14.270] [0 s] n900_vnice_npu_soc.nice_remote_adapter running....
[08-11 16:33:19.525] [60658 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:19.525] [60658 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:19.528] [60658 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 30329
[08-11 16:33:19.529] [60658 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:19.529] [60658 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:19.529] [60658 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:19.529] [60658 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:19.531] [60680 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:19.531] [60680 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:19.536] [60680 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30340
[08-11 16:33:19.536] [60680 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:19.536] [60680 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:19.536] [60680 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:19.536] [60680 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:19.538] [60702 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:19.538] [60702 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:19.539] [60702 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30351
[08-11 16:33:19.540] [60702 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:19.540] [60702 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:19.540] [60702 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:19.540] [60702 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:19.542] [60724 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:19.542] [60724 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:19.542] [60724 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30362
[08-11 16:33:19.543] [60724 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:19.543] [60724 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:19.543] [60724 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:19.543] [60724 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:19.545] [60746 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:19.545] [60746 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:19.546] [60746 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30373
[08-11 16:33:19.546] [60746 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:19.546] [60746 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:19.546] [60746 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:19.546] [60746 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:19.548] [60768 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:19.548] [60768 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:19.549] [60768 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30384
[08-11 16:33:19.549] [60768 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:19.549] [60768 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:19.549] [60768 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:19.549] [60768 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:19.557] [60860 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:19.557] [60860 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:19.558] [60860 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 30430
[08-11 16:33:19.558] [60860 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:19.558] [60860 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:19.558] [60860 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:19.558] [60860 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:19.561] [60882 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:19.561] [60882 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:19.562] [60882 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30441
[08-11 16:33:19.563] [60882 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:19.563] [60882 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:19.563] [60882 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:19.563] [60882 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:19.565] [60904 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:19.565] [60904 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:19.566] [60904 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30452
[08-11 16:33:19.566] [60904 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:19.566] [60904 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:19.566] [60904 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:19.566] [60904 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:19.568] [60926 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:19.568] [60926 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:19.569] [60926 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30463
[08-11 16:33:19.569] [60926 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:19.569] [60926 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:19.569] [60926 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:19.569] [60926 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:19.571] [60948 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:19.571] [60948 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:19.572] [60948 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30474
[08-11 16:33:19.572] [60948 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:19.572] [60948 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:19.572] [60948 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:19.572] [60948 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:19.574] [60970 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:19.574] [60970 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:19.575] [60970 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30485
[08-11 16:33:19.575] [60970 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:19.575] [60970 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:19.575] [60970 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:19.575] [60970 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:19.582] [61042 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:19.582] [61042 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:19.583] [61042 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 30521
[08-11 16:33:19.583] [61042 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:19.583] [61042 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:19.583] [61042 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:19.583] [61042 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:19.585] [61064 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:19.585] [61064 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:19.586] [61064 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30532
[08-11 16:33:19.587] [61064 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:19.587] [61064 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:19.587] [61064 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:19.587] [61064 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:19.589] [61086 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:19.589] [61086 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:19.589] [61086 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30543
[08-11 16:33:19.590] [61086 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:19.590] [61086 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:19.590] [61086 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:19.590] [61086 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:19.592] [61108 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:19.592] [61108 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:19.592] [61108 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30554
[08-11 16:33:19.593] [61108 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:19.593] [61108 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:19.593] [61108 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:19.593] [61108 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:19.595] [61130 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:19.595] [61130 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:19.595] [61130 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30565
[08-11 16:33:19.596] [61130 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:19.596] [61130 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:19.596] [61130 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:19.596] [61130 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:19.598] [61152 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:19.598] [61152 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:19.599] [61152 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30576
[08-11 16:33:19.599] [61152 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:19.599] [61152 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:19.599] [61152 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:19.599] [61152 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:19.606] [61224 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:19.606] [61224 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:19.607] [61224 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 30612
[08-11 16:33:19.607] [61224 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:19.607] [61224 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:19.607] [61224 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:19.607] [61224 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:19.609] [61246 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:19.609] [61246 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:19.610] [61246 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30623
[08-11 16:33:19.611] [61246 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:19.611] [61246 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:19.611] [61246 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:19.611] [61246 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:19.613] [61268 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:19.613] [61268 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:19.613] [61268 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30634
[08-11 16:33:19.614] [61268 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:19.614] [61268 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:19.614] [61268 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:19.614] [61268 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:19.616] [61290 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:19.616] [61290 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:19.616] [61290 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30645
[08-11 16:33:19.617] [61290 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:19.617] [61290 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:19.617] [61290 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:19.617] [61290 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:19.619] [61312 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:19.619] [61312 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:19.619] [61312 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30656
[08-11 16:33:19.620] [61312 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:19.620] [61312 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:19.620] [61312 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:19.620] [61312 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:19.622] [61334 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:19.622] [61334 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:19.623] [61334 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30667
[08-11 16:33:19.623] [61334 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:19.623] [61334 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:19.623] [61334 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:19.623] [61334 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:19.715] [62350 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:19.715] [62350 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:19.716] [62350 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 31175
[08-11 16:33:19.716] [62350 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:19.716] [62350 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:19.716] [62350 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:19.716] [62350 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:19.718] [62372 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:19.718] [62372 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:19.719] [62372 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 31186
[08-11 16:33:19.720] [62372 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:19.720] [62372 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:19.720] [62372 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:19.720] [62372 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:19.722] [62394 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:19.722] [62394 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:19.722] [62394 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 31197
[08-11 16:33:19.723] [62394 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:19.723] [62394 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:19.723] [62394 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:19.723] [62394 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:19.725] [62416 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:19.725] [62416 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:19.725] [62416 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 31208
[08-11 16:33:19.726] [62416 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:19.726] [62416 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:19.726] [62416 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:19.726] [62416 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:19.728] [62438 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:19.728] [62438 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:19.728] [62438 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 31219
[08-11 16:33:19.729] [62438 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:19.729] [62438 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:19.729] [62438 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:19.729] [62438 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:19.731] [62460 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:19.731] [62460 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:19.731] [62460 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 31230
[08-11 16:33:19.732] [62460 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:19.732] [62460 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:19.732] [62460 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:19.732] [62460 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:19.740] [62552 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:19.740] [62552 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:19.741] [62552 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 31276
[08-11 16:33:19.741] [62552 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:19.741] [62552 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:19.741] [62552 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:19.741] [62552 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:19.743] [62574 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:19.743] [62574 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:19.745] [62574 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 31287
[08-11 16:33:19.746] [62574 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:19.746] [62574 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:19.746] [62574 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:19.746] [62574 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:19.748] [62596 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:19.748] [62596 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:19.749] [62596 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 31298
[08-11 16:33:19.749] [62596 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:19.749] [62596 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:19.749] [62596 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:19.749] [62596 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:19.751] [62618 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:19.751] [62618 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:19.752] [62618 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 31309
[08-11 16:33:19.752] [62618 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:19.752] [62618 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:19.752] [62618 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:19.752] [62618 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:19.754] [62640 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:19.754] [62640 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:19.755] [62640 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 31320
[08-11 16:33:19.755] [62640 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:19.755] [62640 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:19.755] [62640 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:19.755] [62640 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:19.757] [62662 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:19.757] [62662 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:19.758] [62662 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 31331
[08-11 16:33:19.758] [62662 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:19.758] [62662 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:19.758] [62662 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:19.758] [62662 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:19.764] [62734 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:19.764] [62734 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:19.765] [62734 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 31367
[08-11 16:33:19.766] [62734 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:19.766] [62734 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:19.766] [62734 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:19.766] [62734 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:19.768] [62756 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:19.768] [62756 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:19.769] [62756 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 31378
[08-11 16:33:19.769] [62756 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:19.769] [62756 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:19.769] [62756 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:19.769] [62756 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:19.772] [62778 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:19.772] [62778 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:19.772] [62778 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 31389
[08-11 16:33:19.773] [62778 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:19.773] [62778 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:19.773] [62778 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:19.773] [62778 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:19.775] [62800 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:19.775] [62800 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:19.776] [62800 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 31400
[08-11 16:33:19.776] [62800 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:19.776] [62800 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:19.776] [62800 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:19.776] [62800 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:19.778] [62822 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:19.778] [62822 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:19.779] [62822 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 31411
[08-11 16:33:19.779] [62822 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:19.779] [62822 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:19.779] [62822 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:19.779] [62822 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:19.781] [62844 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:19.781] [62844 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:19.782] [62844 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 31422
[08-11 16:33:19.782] [62844 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:19.782] [62844 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:19.782] [62844 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:19.782] [62844 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:19.789] [62916 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:19.789] [62916 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:19.790] [62916 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 31458
[08-11 16:33:19.790] [62916 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:19.790] [62916 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:19.790] [62916 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:19.790] [62916 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:19.792] [62938 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:19.792] [62938 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:19.794] [62938 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 31469
[08-11 16:33:19.794] [62938 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:19.794] [62938 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:19.794] [62938 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:19.794] [62938 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:19.796] [62960 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:19.796] [62960 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:19.797] [62960 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 31480
[08-11 16:33:19.797] [62960 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:19.797] [62960 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:19.797] [62960 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:19.797] [62960 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:19.799] [62982 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:19.799] [62982 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:19.800] [62982 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 31491
[08-11 16:33:19.800] [62982 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:19.800] [62982 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:19.800] [62982 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:19.800] [62982 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:19.802] [63004 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:19.802] [63004 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:19.802] [63004 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 31502
[08-11 16:33:19.803] [63004 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:19.803] [63004 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:19.803] [63004 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:19.803] [63004 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:19.805] [63026 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:19.805] [63026 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:19.806] [63026 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 31513
[08-11 16:33:19.806] [63026 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:19.806] [63026 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:19.806] [63026 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:19.806] [63026 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:19.879] [63834 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:19.879] [63834 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:19.880] [63834 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 31917
[08-11 16:33:19.881] [63834 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:19.881] [63834 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:19.881] [63834 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:19.881] [63834 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:19.883] [63860 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:19.883] [63860 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:19.886] [63860 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x20e7b00b to npu, cycles 31930
[08-11 16:33:19.887] [63860 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:19.887] [63860 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:19.887] [63860 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:19.887] [63860 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:19.940] [64460 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:19.940] [64460 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:19.947] [64460 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 32230
[08-11 16:33:19.949] [64460 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:19.949] [64460 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:19.949] [64460 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:19.949] [64460 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:19.952] [64486 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:19.952] [64486 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:19.954] [64486 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x22e7b00b to npu, cycles 32243
[08-11 16:33:19.954] [64486 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:19.954] [64486 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:19.954] [64486 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:19.954] [64486 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:19.986] [64846 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:19.986] [64846 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:19.990] [64846 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 32423
[08-11 16:33:19.990] [64846 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:19.990] [64846 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:19.990] [64846 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:19.990] [64846 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:19.993] [64872 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:19.993] [64872 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:19.993] [64872 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa0e7b00b to npu, cycles 32436
[08-11 16:33:19.994] [64872 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:19.994] [64872 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:19.994] [64872 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:19.994] [64872 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:20.023] [65212 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:20.023] [65212 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:20.025] [65212 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 32606
[08-11 16:33:20.025] [65212 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:20.025] [65212 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:20.025] [65212 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:20.025] [65212 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:20.027] [65234 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:20.027] [65234 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:20.029] [65234 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa207a00b to npu, cycles 32617
[08-11 16:33:20.029] [65234 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:20.029] [65234 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:20.029] [65234 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:20.029] [65234 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:20.103] [66092 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:20.103] [66092 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:20.109] [66092 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 33046
[08-11 16:33:20.111] [66092 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:20.111] [66092 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:20.111] [66092 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:20.111] [66092 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:20.114] [66118 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:20.114] [66118 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:20.115] [66118 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x20e7b00b to npu, cycles 33059
[08-11 16:33:20.116] [66118 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:20.116] [66118 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:20.116] [66118 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:20.116] [66118 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:20.159] [66616 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:20.159] [66616 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:36.962] [66616 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 33308
[08-11 16:33:36.963] [66616 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:36.963] [66616 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:36.963] [66616 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:36.963] [66616 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:36.968] [66642 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:36.968] [66642 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:36.970] [66642 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x22e7b00b to npu, cycles 33321
[08-11 16:33:36.970] [66642 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:36.970] [66642 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:36.970] [66642 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:36.970] [66642 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:37.000] [66930 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:37.000] [66930 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:37.003] [66930 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 33465
[08-11 16:33:37.003] [66930 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:37.003] [66930 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:37.003] [66930 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:37.003] [66930 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:37.006] [66956 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:37.006] [66956 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:37.007] [66956 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa0e7b00b to npu, cycles 33478
[08-11 16:33:37.007] [66956 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:37.007] [66956 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:37.007] [66956 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:37.007] [66956 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:37.033] [67222 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:37.033] [67222 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:37.034] [67222 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 33611
[08-11 16:33:37.035] [67222 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:37.035] [67222 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:37.035] [67222 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:37.035] [67222 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:37.037] [67244 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:37.037] [67244 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:37.039] [67244 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa207a00b to npu, cycles 33622
[08-11 16:33:37.040] [67244 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:37.040] [67244 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:37.040] [67244 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:37.040] [67244 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:37.111] [67988 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:37.111] [67988 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:37.117] [67988 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 33994
[08-11 16:33:37.118] [67988 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:37.118] [67988 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:37.118] [67988 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:37.118] [67988 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:37.121] [68014 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:37.121] [68014 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:37.122] [68014 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x20e7b00b to npu, cycles 34007
[08-11 16:33:37.122] [68014 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:37.122] [68014 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:37.122] [68014 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:37.122] [68014 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:37.173] [68512 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:37.173] [68512 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:44.312] [68512 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 34256
[08-11 16:33:44.313] [68512 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:44.313] [68512 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:44.313] [68512 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:44.313] [68512 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:44.317] [68538 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:44.317] [68538 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:44.322] [68538 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x22e7b00b to npu, cycles 34269
[08-11 16:33:44.322] [68538 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:44.322] [68538 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:44.322] [68538 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:44.322] [68538 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:44.349] [68826 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:44.349] [68826 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:44.360] [68826 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 34413
[08-11 16:33:44.360] [68826 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:44.360] [68826 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:44.360] [68826 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:44.360] [68826 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:44.363] [68852 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:44.363] [68852 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:44.364] [68852 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa0e7b00b to npu, cycles 34426
[08-11 16:33:44.365] [68852 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:44.365] [68852 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:44.365] [68852 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:44.365] [68852 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:44.391] [69118 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:44.391] [69118 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:44.393] [69118 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 34559
[08-11 16:33:44.394] [69118 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:44.394] [69118 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:44.394] [69118 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:44.394] [69118 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:44.397] [69140 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:44.397] [69140 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:44.399] [69140 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa207a00b to npu, cycles 34570
[08-11 16:33:44.400] [69140 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:44.400] [69140 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:44.400] [69140 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:44.400] [69140 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:44.474] [69906 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:44.474] [69906 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:44.480] [69906 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 34953
[08-11 16:33:44.481] [69906 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:44.481] [69906 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:44.481] [69906 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:44.481] [69906 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:44.484] [69932 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:44.484] [69932 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:44.486] [69932 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x20e7b00b to npu, cycles 34966
[08-11 16:33:44.486] [69932 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:44.486] [69932 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:44.486] [69932 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:44.486] [69932 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:44.534] [70448 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:44.534] [70448 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:47.590] [70448 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 35224
[08-11 16:33:47.591] [70448 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:47.591] [70448 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:47.591] [70448 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:47.591] [70448 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:47.594] [70474 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:47.594] [70474 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:47.595] [70474 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x22e7b00b to npu, cycles 35237
[08-11 16:33:47.596] [70474 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:47.596] [70474 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:47.596] [70474 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:47.596] [70474 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:47.626] [70762 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:47.626] [70762 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:47.630] [70762 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 35381
[08-11 16:33:47.630] [70762 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:47.630] [70762 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:47.630] [70762 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:47.630] [70762 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:47.633] [70788 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:47.633] [70788 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:47.635] [70788 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa0e7b00b to npu, cycles 35394
[08-11 16:33:47.635] [70788 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:47.635] [70788 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:47.635] [70788 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:47.635] [70788 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:47.661] [71054 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:47.661] [71054 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:47.663] [71054 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 35527
[08-11 16:33:47.663] [71054 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:47.663] [71054 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:47.663] [71054 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:47.663] [71054 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:47.665] [71076 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:47.665] [71076 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:47.667] [71076 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa207a00b to npu, cycles 35538
[08-11 16:33:47.667] [71076 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:47.667] [71076 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:47.667] [71076 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:47.667] [71076 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:47.739] [71820 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:47.739] [71820 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:47.744] [71820 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 35910
[08-11 16:33:47.745] [71820 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:47.745] [71820 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:47.745] [71820 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:47.745] [71820 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:47.748] [71846 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:47.748] [71846 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 16:33:47.749] [71846 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x20e7b00b to npu, cycles 35923
[08-11 16:33:47.749] [71846 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 16:33:47.749] [71846 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 16:33:47.749] [71846 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 16:33:47.749] [71846 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 16:33:47.794] [72344 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 16:33:47.794] [72344 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
