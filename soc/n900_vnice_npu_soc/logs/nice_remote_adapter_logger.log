[08-11 14:55:57.362] [0 s] n900_vnice_npu_soc.nice_remote_adapter running....
[08-11 14:56:04.716] [101466 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:04.716] [101466 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:04.717] [101466 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 50733
[08-11 14:56:04.718] [101466 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:04.718] [101466 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:04.718] [101466 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:04.718] [101466 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:04.720] [101488 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:04.720] [101488 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:04.722] [101488 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 50744
[08-11 14:56:04.722] [101488 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:04.722] [101488 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:04.722] [101488 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:04.722] [101488 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:04.724] [101510 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:04.724] [101510 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:04.725] [101510 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 50755
[08-11 14:56:04.725] [101510 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:04.725] [101510 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:04.725] [101510 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:04.725] [101510 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:04.727] [101532 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:04.727] [101532 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:04.728] [101532 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 50766
[08-11 14:56:04.728] [101532 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:04.728] [101532 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:04.728] [101532 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:04.728] [101532 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:04.730] [101554 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:04.730] [101554 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:04.730] [101554 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 50777
[08-11 14:56:04.731] [101554 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:04.731] [101554 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:04.731] [101554 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:04.731] [101554 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:04.733] [101576 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:04.733] [101576 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:04.733] [101576 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 50788
[08-11 14:56:04.733] [101576 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:04.733] [101576 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:04.733] [101576 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:04.733] [101576 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:04.741] [101668 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:04.741] [101668 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:04.741] [101668 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 50834
[08-11 14:56:04.742] [101668 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:04.742] [101668 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:04.742] [101668 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:04.742] [101668 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:04.744] [101690 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:04.744] [101690 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:04.745] [101690 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 50845
[08-11 14:56:04.745] [101690 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:04.745] [101690 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:04.745] [101690 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:04.745] [101690 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:04.747] [101712 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:04.747] [101712 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:04.748] [101712 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 50856
[08-11 14:56:04.748] [101712 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:04.748] [101712 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:04.748] [101712 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:04.748] [101712 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:04.750] [101734 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:04.750] [101734 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:04.751] [101734 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 50867
[08-11 14:56:04.751] [101734 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:04.751] [101734 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:04.751] [101734 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:04.751] [101734 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:04.753] [101756 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:04.753] [101756 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:04.753] [101756 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 50878
[08-11 14:56:04.754] [101756 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:04.754] [101756 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:04.754] [101756 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:04.754] [101756 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:04.756] [101778 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:04.756] [101778 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:04.756] [101778 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 50889
[08-11 14:56:04.757] [101778 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:04.757] [101778 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:04.757] [101778 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:04.757] [101778 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:04.762] [101850 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:04.762] [101850 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:04.763] [101850 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 50925
[08-11 14:56:04.764] [101850 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:04.764] [101850 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:04.764] [101850 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:04.764] [101850 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:04.766] [101872 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:04.766] [101872 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:04.767] [101872 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 50936
[08-11 14:56:04.767] [101872 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:04.767] [101872 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:04.767] [101872 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:04.767] [101872 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:04.769] [101894 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:04.769] [101894 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:04.770] [101894 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 50947
[08-11 14:56:04.770] [101894 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:04.770] [101894 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:04.770] [101894 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:04.770] [101894 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:04.772] [101916 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:04.772] [101916 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:04.773] [101916 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 50958
[08-11 14:56:04.773] [101916 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:04.773] [101916 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:04.773] [101916 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:04.773] [101916 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:04.775] [101938 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:04.775] [101938 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:04.775] [101938 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 50969
[08-11 14:56:04.776] [101938 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:04.776] [101938 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:04.776] [101938 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:04.776] [101938 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:04.777] [101960 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:04.777] [101960 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:04.778] [101960 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 50980
[08-11 14:56:04.778] [101960 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:04.778] [101960 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:04.778] [101960 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:04.778] [101960 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:04.784] [102032 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:04.784] [102032 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:04.785] [102032 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 51016
[08-11 14:56:04.785] [102032 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:04.785] [102032 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:04.785] [102032 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:04.785] [102032 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:04.787] [102054 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:04.787] [102054 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:04.788] [102054 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 51027
[08-11 14:56:04.789] [102054 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:04.789] [102054 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:04.789] [102054 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:04.789] [102054 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:04.791] [102076 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:04.791] [102076 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:04.792] [102076 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 51038
[08-11 14:56:04.792] [102076 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:04.792] [102076 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:04.792] [102076 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:04.792] [102076 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:04.794] [102098 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:04.794] [102098 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:04.794] [102098 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 51049
[08-11 14:56:04.795] [102098 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:04.795] [102098 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:04.795] [102098 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:04.795] [102098 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:04.797] [102120 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:04.797] [102120 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:04.797] [102120 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 51060
[08-11 14:56:04.798] [102120 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:04.798] [102120 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:04.798] [102120 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:04.798] [102120 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:04.799] [102142 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:04.799] [102142 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:04.800] [102142 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 51071
[08-11 14:56:04.800] [102142 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:04.800] [102142 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:04.800] [102142 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:04.800] [102142 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:04.880] [103158 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:04.880] [103158 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:04.881] [103158 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 51579
[08-11 14:56:04.881] [103158 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:04.881] [103158 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:04.881] [103158 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:04.881] [103158 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:04.883] [103180 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:04.883] [103180 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:04.884] [103180 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 51590
[08-11 14:56:04.884] [103180 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:04.884] [103180 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:04.884] [103180 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:04.884] [103180 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:04.886] [103202 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:04.886] [103202 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:04.887] [103202 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 51601
[08-11 14:56:04.887] [103202 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:04.887] [103202 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:04.887] [103202 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:04.887] [103202 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:04.889] [103224 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:04.889] [103224 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:04.889] [103224 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 51612
[08-11 14:56:04.890] [103224 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:04.890] [103224 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:04.890] [103224 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:04.890] [103224 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:04.891] [103246 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:04.891] [103246 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:04.892] [103246 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 51623
[08-11 14:56:04.892] [103246 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:04.892] [103246 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:04.892] [103246 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:04.892] [103246 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:04.894] [103268 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:04.894] [103268 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:04.894] [103268 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 51634
[08-11 14:56:04.895] [103268 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:04.895] [103268 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:04.895] [103268 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:04.895] [103268 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:04.902] [103360 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:04.902] [103360 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:04.903] [103360 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 51680
[08-11 14:56:04.903] [103360 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:04.903] [103360 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:04.903] [103360 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:04.903] [103360 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:04.905] [103382 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:04.905] [103382 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:04.906] [103382 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 51691
[08-11 14:56:04.906] [103382 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:04.906] [103382 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:04.906] [103382 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:04.906] [103382 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:04.908] [103404 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:04.908] [103404 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:04.908] [103404 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 51702
[08-11 14:56:04.909] [103404 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:04.909] [103404 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:04.909] [103404 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:04.909] [103404 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:04.910] [103426 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:04.910] [103426 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:04.911] [103426 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 51713
[08-11 14:56:04.911] [103426 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:04.911] [103426 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:04.911] [103426 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:04.911] [103426 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:04.913] [103448 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:04.913] [103448 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:04.914] [103448 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 51724
[08-11 14:56:04.914] [103448 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:04.914] [103448 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:04.914] [103448 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:04.914] [103448 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:04.916] [103470 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:04.916] [103470 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:04.916] [103470 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 51735
[08-11 14:56:04.917] [103470 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:04.917] [103470 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:04.917] [103470 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:04.917] [103470 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:04.922] [103542 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:04.922] [103542 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:04.923] [103542 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 51771
[08-11 14:56:04.923] [103542 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:04.923] [103542 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:04.923] [103542 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:04.923] [103542 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:04.925] [103564 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:04.925] [103564 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:04.926] [103564 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 51782
[08-11 14:56:04.926] [103564 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:04.926] [103564 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:04.926] [103564 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:04.926] [103564 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:04.928] [103586 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:04.928] [103586 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:04.928] [103586 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 51793
[08-11 14:56:04.929] [103586 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:04.929] [103586 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:04.929] [103586 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:04.929] [103586 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:04.931] [103608 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:04.931] [103608 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:04.931] [103608 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 51804
[08-11 14:56:04.931] [103608 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:04.931] [103608 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:04.931] [103608 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:04.931] [103608 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:04.933] [103630 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:04.933] [103630 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:04.934] [103630 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 51815
[08-11 14:56:04.934] [103630 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:04.934] [103630 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:04.934] [103630 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:04.934] [103630 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:04.936] [103652 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:04.936] [103652 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:04.936] [103652 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 51826
[08-11 14:56:04.937] [103652 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:04.937] [103652 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:04.937] [103652 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:04.937] [103652 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:04.942] [103724 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:04.942] [103724 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:04.943] [103724 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 51862
[08-11 14:56:04.943] [103724 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:04.943] [103724 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:04.943] [103724 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:04.943] [103724 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:04.945] [103746 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:04.945] [103746 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:04.946] [103746 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 51873
[08-11 14:56:04.946] [103746 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:04.946] [103746 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:04.946] [103746 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:04.946] [103746 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:04.948] [103768 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:04.948] [103768 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:04.948] [103768 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 51884
[08-11 14:56:04.949] [103768 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:04.949] [103768 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:04.949] [103768 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:04.949] [103768 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:04.951] [103790 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:04.951] [103790 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:04.951] [103790 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 51895
[08-11 14:56:04.951] [103790 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:04.951] [103790 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:04.951] [103790 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:04.951] [103790 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:04.953] [103812 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:04.953] [103812 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:04.954] [103812 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 51906
[08-11 14:56:04.954] [103812 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:04.954] [103812 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:04.954] [103812 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:04.954] [103812 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:04.956] [103834 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:04.956] [103834 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:04.956] [103834 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 51917
[08-11 14:56:04.957] [103834 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:04.957] [103834 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:04.957] [103834 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:04.957] [103834 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:05.023] [104678 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:05.023] [104678 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:05.024] [104678 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 52339
[08-11 14:56:05.024] [104678 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:05.024] [104678 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:05.024] [104678 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:05.024] [104678 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:05.026] [104704 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:05.026] [104704 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:05.027] [104704 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x20e7b00b to npu, cycles 52352
[08-11 14:56:05.027] [104704 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:05.027] [104704 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:05.027] [104704 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:05.027] [104704 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:05.075] [105304 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:05.075] [105304 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:05.078] [105304 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 52652
[08-11 14:56:05.078] [105304 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:05.078] [105304 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:05.078] [105304 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:05.078] [105304 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:05.080] [105330 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:05.080] [105330 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:05.081] [105330 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x22e7b00b to npu, cycles 52665
[08-11 14:56:05.082] [105330 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:05.082] [105330 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:05.082] [105330 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:05.082] [105330 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:05.109] [105670 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:05.109] [105670 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:05.113] [105670 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 52835
[08-11 14:56:05.114] [105670 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:05.114] [105670 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:05.114] [105670 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:05.114] [105670 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:05.116] [105696 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:05.116] [105696 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:05.117] [105696 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa0e7b00b to npu, cycles 52848
[08-11 14:56:05.117] [105696 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:05.117] [105696 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:05.117] [105696 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:05.117] [105696 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:05.144] [106036 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:05.144] [106036 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:05.145] [106036 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 53018
[08-11 14:56:05.145] [106036 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:05.145] [106036 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:05.145] [106036 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:05.145] [106036 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:05.147] [106058 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:05.147] [106058 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:05.148] [106058 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa207a00b to npu, cycles 53029
[08-11 14:56:05.148] [106058 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:05.148] [106058 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:05.148] [106058 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:05.148] [106058 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:05.216] [106922 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:05.216] [106922 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:05.218] [106922 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 53461
[08-11 14:56:05.219] [106922 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:05.219] [106922 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:05.219] [106922 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:05.219] [106922 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:05.221] [106948 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:05.221] [106948 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:56:05.222] [106948 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x20e7b00b to npu, cycles 53474
[08-11 14:56:05.222] [106948 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:56:05.222] [106948 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:56:05.222] [106948 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:56:05.222] [106948 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:56:05.274] [107464 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:56:05.274] [107464 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:58:12.918] [107464 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 53732
[08-11 14:58:12.919] [107464 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:58:12.919] [107464 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:58:12.919] [107464 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:58:12.919] [107464 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:58:12.922] [107490 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:58:12.922] [107490 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:58:12.924] [107490 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x22e7b00b to npu, cycles 53745
[08-11 14:58:12.924] [107490 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:58:12.924] [107490 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:58:12.924] [107490 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:58:12.924] [107490 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:58:12.953] [107778 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:58:12.953] [107778 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:58:12.961] [107778 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 53889
[08-11 14:58:12.961] [107778 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:58:12.962] [107778 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:58:12.962] [107778 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:58:12.962] [107778 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:58:12.965] [107804 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:58:12.965] [107804 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:58:12.967] [107804 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa0e7b00b to npu, cycles 53902
[08-11 14:58:12.968] [107804 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:58:12.968] [107804 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:58:12.968] [107804 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:58:12.968] [107804 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:58:12.989] [108070 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:58:12.989] [108070 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:58:12.993] [108070 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 54035
[08-11 14:58:12.994] [108070 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:58:12.994] [108070 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:58:12.994] [108070 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:58:12.994] [108070 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:58:12.997] [108092 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:58:12.997] [108092 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:58:12.998] [108092 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa207a00b to npu, cycles 54046
[08-11 14:58:12.999] [108092 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:58:12.999] [108092 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:58:12.999] [108092 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:58:12.999] [108092 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:58:13.065] [108886 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:58:13.065] [108886 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:58:13.068] [108886 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 54443
[08-11 14:58:13.069] [108886 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:58:13.069] [108886 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:58:13.069] [108886 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:58:13.069] [108886 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:58:13.071] [108912 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:58:13.071] [108912 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:58:13.072] [108912 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x20e7b00b to npu, cycles 54456
[08-11 14:58:13.072] [108912 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:58:13.072] [108912 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:58:13.072] [108912 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:58:13.072] [108912 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:58:13.105] [109376 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:58:13.105] [109376 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:58:45.602] [109376 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 54688
[08-11 14:58:45.603] [109376 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:58:45.603] [109376 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:58:45.603] [109376 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:58:45.603] [109376 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:58:45.606] [109402 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:58:45.606] [109402 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:58:45.608] [109402 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x22e7b00b to npu, cycles 54701
[08-11 14:58:45.608] [109402 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:58:45.608] [109402 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:58:45.608] [109402 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:58:45.608] [109402 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:58:45.640] [109690 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:58:45.640] [109690 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:58:45.645] [109690 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 54845
[08-11 14:58:45.647] [109690 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:58:45.647] [109690 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:58:45.647] [109690 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:58:45.647] [109690 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:58:45.649] [109716 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:58:45.649] [109716 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:58:45.650] [109716 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa0e7b00b to npu, cycles 54858
[08-11 14:58:45.650] [109716 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:58:45.650] [109716 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:58:45.650] [109716 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:58:45.650] [109716 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:58:45.674] [109982 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:58:45.674] [109982 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:58:45.675] [109982 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 54991
[08-11 14:58:45.675] [109982 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:58:45.675] [109982 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:58:45.675] [109982 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:58:45.675] [109982 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:58:45.677] [110004 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:58:45.677] [110004 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:58:45.678] [110004 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa207a00b to npu, cycles 55002
[08-11 14:58:45.678] [110004 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:58:45.678] [110004 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:58:45.678] [110004 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:58:45.678] [110004 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:58:45.743] [110780 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:58:45.743] [110780 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:58:45.748] [110780 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 55390
[08-11 14:58:45.750] [110780 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:58:45.750] [110780 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:58:45.750] [110780 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:58:45.750] [110780 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:58:45.752] [110806 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:58:45.752] [110806 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:58:45.753] [110806 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x20e7b00b to npu, cycles 55403
[08-11 14:58:45.753] [110806 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:58:45.753] [110806 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:58:45.753] [110806 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:58:45.753] [110806 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:58:45.796] [111322 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:58:45.796] [111322 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:58:51.573] [111322 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 55661
[08-11 14:58:51.573] [111322 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:58:51.573] [111322 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:58:51.573] [111322 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:58:51.573] [111322 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:58:51.578] [111348 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:58:51.578] [111348 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:58:51.581] [111348 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x22e7b00b to npu, cycles 55674
[08-11 14:58:51.582] [111348 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:58:51.582] [111348 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:58:51.582] [111348 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:58:51.582] [111348 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:58:51.607] [111636 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:58:51.607] [111636 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:58:51.611] [111636 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 55818
[08-11 14:58:51.612] [111636 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:58:51.612] [111636 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:58:51.612] [111636 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:58:51.612] [111636 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:58:51.615] [111662 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:58:51.615] [111662 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:58:51.617] [111662 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa0e7b00b to npu, cycles 55831
[08-11 14:58:51.617] [111662 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:58:51.617] [111662 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:58:51.617] [111662 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:58:51.617] [111662 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:58:51.640] [111928 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:58:51.640] [111928 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:58:51.641] [111928 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 55964
[08-11 14:58:51.642] [111928 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:58:51.642] [111928 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:58:51.642] [111928 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:58:51.642] [111928 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:58:51.644] [111950 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:58:51.644] [111950 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:58:51.645] [111950 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa207a00b to npu, cycles 55975
[08-11 14:58:51.646] [111950 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:58:51.646] [111950 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:58:51.646] [111950 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:58:51.646] [111950 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:58:51.712] [112764 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:58:51.712] [112764 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:58:51.716] [112764 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 56382
[08-11 14:58:51.717] [112764 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:58:51.717] [112764 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:58:51.717] [112764 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:58:51.717] [112764 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:58:51.720] [112790 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:58:51.720] [112790 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:58:51.721] [112790 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x20e7b00b to npu, cycles 56395
[08-11 14:58:51.721] [112790 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:58:51.721] [112790 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:58:51.721] [112790 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:58:51.721] [112790 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:58:51.762] [113288 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:58:51.763] [113288 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:59:07.617] [113288 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 56644
[08-11 14:59:07.618] [113288 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:59:07.618] [113288 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:59:07.618] [113288 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:59:07.618] [113288 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:59:07.621] [113314 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:59:07.621] [113314 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:59:07.622] [113314 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x22e7b00b to npu, cycles 56657
[08-11 14:59:07.622] [113314 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:59:07.622] [113314 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:59:07.622] [113314 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:59:07.622] [113314 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:59:07.646] [113602 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:59:07.646] [113602 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:59:07.650] [113602 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 56801
[08-11 14:59:07.651] [113602 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:59:07.651] [113602 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:59:07.651] [113602 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:59:07.651] [113602 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:59:07.653] [113628 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:59:07.653] [113628 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:59:07.655] [113628 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa0e7b00b to npu, cycles 56814
[08-11 14:59:07.655] [113628 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:59:07.655] [113628 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:59:07.655] [113628 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:59:07.655] [113628 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:59:07.676] [113894 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:59:07.676] [113894 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:59:07.677] [113894 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 56947
[08-11 14:59:07.677] [113894 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:59:07.677] [113894 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:59:07.677] [113894 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:59:07.677] [113894 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:59:07.679] [113916 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:59:07.679] [113916 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:59:07.680] [113916 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa207a00b to npu, cycles 56958
[08-11 14:59:07.680] [113916 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:59:07.680] [113916 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:59:07.680] [113916 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:59:07.680] [113916 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:59:07.744] [114692 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:59:07.744] [114692 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:59:07.749] [114692 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 57346
[08-11 14:59:07.751] [114692 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:59:07.751] [114692 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:59:07.751] [114692 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:59:07.751] [114692 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:59:07.753] [114718 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:59:07.753] [114718 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:59:07.754] [114718 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x20e7b00b to npu, cycles 57359
[08-11 14:59:07.754] [114718 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:59:07.754] [114718 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:59:07.754] [114718 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:59:07.754] [114718 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:59:07.791] [115182 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:59:07.791] [115182 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:59:13.022] [115182 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 57591
[08-11 14:59:13.022] [115182 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:59:13.022] [115182 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:59:13.022] [115182 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:59:13.022] [115182 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:59:13.026] [115208 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:59:13.026] [115208 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:59:13.027] [115208 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x22e7b00b to npu, cycles 57604
[08-11 14:59:13.028] [115208 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:59:13.028] [115208 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:59:13.028] [115208 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:59:13.028] [115208 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:59:13.051] [115450 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:59:13.051] [115450 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:59:13.053] [115450 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 57725
[08-11 14:59:13.054] [115450 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:59:13.054] [115450 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:59:13.054] [115450 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:59:13.054] [115450 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:59:13.056] [115476 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:59:13.056] [115476 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:59:13.057] [115476 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa0e7b00b to npu, cycles 57738
[08-11 14:59:13.057] [115476 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:59:13.057] [115476 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:59:13.057] [115476 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:59:13.057] [115476 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:59:13.074] [115694 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:59:13.074] [115694 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:59:13.076] [115694 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 57847
[08-11 14:59:13.076] [115694 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:59:13.076] [115694 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:59:13.076] [115694 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:59:13.076] [115694 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:59:13.078] [115716 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:59:13.078] [115716 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:59:13.082] [115716 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa207a00b to npu, cycles 57858
[08-11 14:59:13.083] [115716 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:59:13.083] [115716 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:59:13.083] [115716 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:59:13.083] [115716 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:59:13.144] [116478 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:59:13.144] [116478 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:59:13.147] [116478 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 58239
[08-11 14:59:13.148] [116478 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:59:13.148] [116478 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:59:13.148] [116478 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:59:13.148] [116478 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:59:13.150] [116504 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:59:13.150] [116504 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:59:13.151] [116504 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x20e7b00b to npu, cycles 58252
[08-11 14:59:13.151] [116504 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:59:13.151] [116504 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:59:13.151] [116504 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:59:13.151] [116504 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:59:13.183] [116950 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:59:13.183] [116950 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:59:17.988] [116950 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 58475
[08-11 14:59:17.988] [116950 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:59:17.988] [116950 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:59:17.988] [116950 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:59:17.988] [116950 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:59:17.992] [116976 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:59:17.992] [116976 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:59:17.994] [116976 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x22e7b00b to npu, cycles 58488
[08-11 14:59:17.994] [116976 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:59:17.994] [116976 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:59:17.994] [116976 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:59:17.994] [116976 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:59:18.011] [117214 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:59:18.011] [117214 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:59:18.015] [117214 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 58607
[08-11 14:59:18.015] [117214 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:59:18.015] [117214 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:59:18.015] [117214 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:59:18.015] [117214 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:59:18.017] [117240 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:59:18.017] [117240 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:59:18.019] [117240 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa0e7b00b to npu, cycles 58620
[08-11 14:59:18.019] [117240 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:59:18.019] [117240 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:59:18.019] [117240 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:59:18.019] [117240 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:59:18.035] [117458 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:59:18.035] [117458 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:59:18.037] [117458 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 58729
[08-11 14:59:18.037] [117458 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:59:18.037] [117458 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:59:18.037] [117458 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:59:18.037] [117458 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:59:18.039] [117480 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:59:18.039] [117480 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:59:18.041] [117480 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa207a00b to npu, cycles 58740
[08-11 14:59:18.041] [117480 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:59:18.041] [117480 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:59:18.041] [117480 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:59:18.041] [117480 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:59:18.094] [118224 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:59:18.094] [118224 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:59:18.098] [118224 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 59112
[08-11 14:59:18.099] [118224 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:59:18.099] [118224 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:59:18.099] [118224 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:59:18.099] [118224 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:59:18.101] [118250 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:59:18.101] [118250 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:59:18.102] [118250 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x20e7b00b to npu, cycles 59125
[08-11 14:59:18.102] [118250 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:59:18.102] [118250 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:59:18.102] [118250 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:59:18.102] [118250 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:59:18.142] [118798 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:59:18.142] [118798 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:59:23.042] [118798 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 59399
[08-11 14:59:23.043] [118798 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:59:23.043] [118798 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:59:23.043] [118798 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:59:23.043] [118798 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:59:23.047] [118824 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:59:23.047] [118824 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:59:23.048] [118824 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x22e7b00b to npu, cycles 59412
[08-11 14:59:23.048] [118824 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:59:23.048] [118824 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:59:23.048] [118824 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:59:23.048] [118824 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:59:23.069] [119114 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:59:23.069] [119114 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:59:23.071] [119114 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 59557
[08-11 14:59:23.072] [119114 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:59:23.072] [119114 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:59:23.072] [119114 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:59:23.072] [119114 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:59:23.074] [119140 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:59:23.074] [119140 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:59:23.075] [119140 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa0e7b00b to npu, cycles 59570
[08-11 14:59:23.075] [119140 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:59:23.075] [119140 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:59:23.075] [119140 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:59:23.075] [119140 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:59:23.093] [119408 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:59:23.093] [119408 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:59:23.094] [119408 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 59704
[08-11 14:59:23.094] [119408 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:59:23.094] [119408 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:59:23.094] [119408 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:59:23.094] [119408 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:59:23.096] [119430 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:59:23.096] [119430 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:59:23.097] [119430 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa207a00b to npu, cycles 59715
[08-11 14:59:23.097] [119430 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:59:23.097] [119430 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:59:23.097] [119430 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:59:23.097] [119430 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:59:23.154] [120246 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:59:23.154] [120246 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:59:23.157] [120246 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 60123
[08-11 14:59:23.157] [120246 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:59:23.157] [120246 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:59:23.157] [120246 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:59:23.157] [120246 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:59:23.159] [120272 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:59:23.159] [120272 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:59:23.160] [120272 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x20e7b00b to npu, cycles 60136
[08-11 14:59:23.160] [120272 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:59:23.160] [120272 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:59:23.160] [120272 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:59:23.160] [120272 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:59:23.195] [120772 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:59:23.195] [120772 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:59:46.217] [120772 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 60386
[08-11 14:59:46.217] [120772 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:59:46.217] [120772 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:59:46.217] [120772 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:59:46.217] [120772 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:59:46.221] [120798 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:59:46.221] [120798 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:59:46.222] [120798 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x22e7b00b to npu, cycles 60399
[08-11 14:59:46.223] [120798 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:59:46.223] [120798 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:59:46.223] [120798 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:59:46.223] [120798 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:59:46.244] [121088 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:59:46.244] [121088 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:59:46.246] [121088 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 60544
[08-11 14:59:46.247] [121088 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:59:46.247] [121088 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:59:46.247] [121088 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:59:46.247] [121088 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:59:46.249] [121114 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:59:46.249] [121114 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:59:46.250] [121114 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa0e7b00b to npu, cycles 60557
[08-11 14:59:46.250] [121114 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:59:46.250] [121114 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:59:46.250] [121114 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:59:46.250] [121114 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:59:46.270] [121382 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:59:46.270] [121382 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:59:46.272] [121382 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 60691
[08-11 14:59:46.272] [121382 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:59:46.272] [121382 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:59:46.272] [121382 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:59:46.272] [121382 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:59:46.274] [121404 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:59:46.274] [121404 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:59:46.275] [121404 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa207a00b to npu, cycles 60702
[08-11 14:59:46.276] [121404 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:59:46.276] [121404 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:59:46.276] [121404 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:59:46.276] [121404 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:59:46.326] [122182 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:59:46.326] [122182 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:59:46.333] [122182 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 61091
[08-11 14:59:46.335] [122182 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:59:46.335] [122182 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:59:46.335] [122182 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:59:46.335] [122182 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:59:46.337] [122208 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:59:46.337] [122208 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:59:46.338] [122208 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x20e7b00b to npu, cycles 61104
[08-11 14:59:46.339] [122208 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:59:46.339] [122208 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:59:46.339] [122208 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:59:46.339] [122208 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:59:46.371] [122674 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:59:46.371] [122674 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:59:51.292] [122674 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 61337
[08-11 14:59:51.293] [122674 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:59:51.293] [122674 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:59:51.293] [122674 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:59:51.293] [122674 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:59:51.296] [122700 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:59:51.297] [122700 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:59:51.298] [122700 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x22e7b00b to npu, cycles 61350
[08-11 14:59:51.298] [122700 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:59:51.298] [122700 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:59:51.298] [122700 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:59:51.298] [122700 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:59:51.319] [122972 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:59:51.319] [122972 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:59:51.322] [122972 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 61486
[08-11 14:59:51.322] [122972 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:59:51.322] [122972 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:59:51.322] [122972 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:59:51.322] [122972 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:59:51.324] [122998 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:59:51.324] [122998 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:59:51.325] [122998 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa0e7b00b to npu, cycles 61499
[08-11 14:59:51.326] [122998 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:59:51.326] [122998 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:59:51.326] [122998 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:59:51.326] [122998 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:59:51.340] [123216 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:59:51.340] [123216 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:59:51.341] [123216 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 61608
[08-11 14:59:51.342] [123216 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:59:51.342] [123216 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:59:51.342] [123216 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:59:51.342] [123216 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:59:51.343] [123238 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:59:51.343] [123238 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:59:51.344] [123238 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa207a00b to npu, cycles 61619
[08-11 14:59:51.344] [123238 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:59:51.344] [123238 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:59:51.344] [123238 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:59:51.344] [123238 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:59:51.395] [123964 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:59:51.395] [123964 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:59:51.398] [123964 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 61982
[08-11 14:59:51.399] [123964 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:59:51.399] [123964 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:59:51.399] [123964 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:59:51.399] [123964 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:59:51.401] [123990 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:59:51.401] [123990 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:59:51.401] [123990 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x20e7b00b to npu, cycles 61995
[08-11 14:59:51.402] [123990 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:59:51.402] [123990 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:59:51.402] [123990 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:59:51.402] [123990 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:59:51.433] [124438 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:59:51.433] [124438 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:59:56.753] [124438 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 62219
[08-11 14:59:56.754] [124438 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:59:56.754] [124438 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:59:56.754] [124438 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:59:56.754] [124438 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:59:56.760] [124464 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:59:56.760] [124464 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:59:56.763] [124464 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x22e7b00b to npu, cycles 62232
[08-11 14:59:56.764] [124464 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:59:56.764] [124464 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:59:56.764] [124464 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:59:56.764] [124464 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:59:56.788] [124702 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:59:56.788] [124702 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:59:56.794] [124702 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 62351
[08-11 14:59:56.795] [124702 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:59:56.795] [124702 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:59:56.795] [124702 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:59:56.795] [124702 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:59:56.797] [124728 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:59:56.798] [124728 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:59:56.800] [124728 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa0e7b00b to npu, cycles 62364
[08-11 14:59:56.801] [124728 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:59:56.801] [124728 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:59:56.801] [124728 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:59:56.801] [124728 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:59:56.820] [124946 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:59:56.820] [124946 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:59:56.821] [124946 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 62473
[08-11 14:59:56.822] [124946 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:59:56.822] [124946 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:59:56.822] [124946 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:59:56.822] [124946 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:59:56.824] [124968 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:59:56.824] [124968 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:59:56.826] [124968 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa207a00b to npu, cycles 62484
[08-11 14:59:56.826] [124968 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:59:56.826] [124968 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:59:56.826] [124968 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:59:56.827] [124968 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:59:56.910] [125710 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:59:56.910] [125710 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:59:56.915] [125710 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 62855
[08-11 14:59:56.916] [125710 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:59:56.916] [125710 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:59:56.916] [125710 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:59:56.916] [125710 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:59:56.918] [125736 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:59:56.918] [125736 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 14:59:56.920] [125736 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x20e7b00b to npu, cycles 62868
[08-11 14:59:56.920] [125736 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 14:59:56.920] [125736 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 14:59:56.920] [125736 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 14:59:56.920] [125736 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 14:59:56.959] [126170 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 14:59:56.959] [126170 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 15:00:03.940] [126170 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 63085
[08-11 15:00:03.941] [126170 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 15:00:03.941] [126170 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 15:00:03.941] [126170 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 15:00:03.941] [126170 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 15:00:03.945] [126196 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 15:00:03.945] [126196 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 15:00:03.946] [126196 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x22e7b00b to npu, cycles 63098
[08-11 15:00:03.947] [126196 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 15:00:03.947] [126196 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 15:00:03.947] [126196 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 15:00:03.947] [126196 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 15:00:03.978] [126504 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 15:00:03.978] [126504 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 15:00:03.981] [126504 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 63252
[08-11 15:00:03.981] [126504 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 15:00:03.981] [126504 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 15:00:03.981] [126504 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 15:00:03.981] [126504 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 15:00:03.984] [126530 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 15:00:03.984] [126530 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 15:00:03.985] [126530 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa0e7b00b to npu, cycles 63265
[08-11 15:00:03.985] [126530 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 15:00:03.985] [126530 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 15:00:03.985] [126530 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 15:00:03.985] [126530 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 15:00:04.006] [126768 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 15:00:04.006] [126768 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 15:00:04.006] [126768 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 63384
[08-11 15:00:04.007] [126768 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 15:00:04.007] [126768 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 15:00:04.007] [126768 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 15:00:04.007] [126768 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 15:00:04.009] [126790 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 15:00:04.009] [126790 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 15:00:04.010] [126790 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa207a00b to npu, cycles 63395
[08-11 15:00:04.011] [126790 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 15:00:04.011] [126790 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 15:00:04.011] [126790 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 15:00:04.011] [126790 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 15:00:04.081] [127604 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 15:00:04.081] [127604 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 15:00:04.085] [127604 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 63802
[08-11 15:00:04.085] [127604 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 15:00:04.085] [127604 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 15:00:04.085] [127604 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 15:00:04.085] [127604 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 15:00:04.088] [127630 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 15:00:04.088] [127630 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-11 15:00:04.089] [127630 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x20e7b00b to npu, cycles 63815
[08-11 15:00:04.089] [127630 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-11 15:00:04.089] [127630 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-11 15:00:04.089] [127630 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-11 15:00:04.089] [127630 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-11 15:00:04.135] [128148 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-11 15:00:04.135] [128148 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
