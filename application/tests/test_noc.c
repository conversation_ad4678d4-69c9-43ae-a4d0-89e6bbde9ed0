#include <stdio.h>
#include "hardware_inst_data.h"
#include "minicpmv_basic.h"
#include "nuclei_sdk_soc.h"
#include "high_level.h"
#include "primitive.h"

inline static void init_vnice() {
    __RV_CSR_SET(CSR_MSTATUS, MSTATUS_XS);
    volatile int l = __riscv_vsetvl_e32m1(4);
}


inline static uint32_t get_tensor_size(const Tensor *tensor) {
    return tensor->dim2 * tensor->byte_stride2_u;
}

int test_allreduce() {
    init_vnice();

    // DEBUG: 打印测试开始信息
    debug_printf("=== AllReduce Test Start ===\n");
    debug_printf("Input1: base=0x%08x, dim=[%d,%d,%d], stride=[%d,%d]\n",
                SCRATCHPAD0_ADDR, 256, 8, 1, 32*16, 32*128);
    debug_printf("Input2: base=0x%08x, dim=[%d,%d,%d], stride=[%d,%d]\n",
                SCRATCHPAD1_ADDR, 256, 8, 1, 32*16, 32*128);

    Tensor input1 = (Tensor){
        .base_addr      = SCRATCHPAD0_ADDR,
        .dim0           = 256,
        .dim1           = 8,
        .dim2           = 1,
        .byte_stride1_u = 32 * 16,
        .byte_stride2_u = 32 * 128,
        .width          = WIDTH_16,
        .type           = TYPE_BF
    };

    Tensor input2 = (Tensor){
        .base_addr      = SCRATCHPAD1_ADDR,
        .dim0           = 256,
        .dim1           = 8,
        .dim2           = 1,
        .byte_stride1_u = 32 * 16,
        .byte_stride2_u = 32 * 128,
        .width          = WIDTH_16,
        .type           = TYPE_BF
    };

    Tensor buf_shape = (Tensor) {
        .base_addr      = SCRATCHPAD2_ADDR,
        .dim0           = 256,
        .dim1           = 1,
        .dim2           = 1,
        .byte_stride1_u = 32 * 16,
        .byte_stride2_u = 32 * 16,
        .width          = WIDTH_16,
        .type           = TYPE_BF

    };

    InterMemoryArray inter_mem = {
        .memory = (InterMemory[]){
            {
                .base_addr = SCRATCHPAD0_ADDR + get_tensor_size(&input1),
                .byte_size = get_tensor_size(&buf_shape)
            },
            {
                .base_addr = SCRATCHPAD1_ADDR + get_tensor_size(&input2),
                .byte_size = get_tensor_size(&buf_shape)
            }
        },
        .length = 2
    };

    debug_printf("InterMemory[0]: base=0x%08x, size=%d\n",
                inter_mem.memory[0].base_addr, inter_mem.memory[0].byte_size);
    debug_printf("InterMemory[1]: base=0x%08x, size=%d\n",
                inter_mem.memory[1].base_addr, inter_mem.memory[1].byte_size);

    // 调用验证函数
    extern void verify_index_alignment();
    verify_index_alignment();

    debug_printf("=== Starting ReduceScatter ===\n");
    ReduceScatter(&input1, &input2, &inter_mem);

    debug_printf("=== Starting AllGather ===\n");
    AllGather(&input1, &input2);

    debug_printf("=== AllReduce Test End ===\n");

    return 0;
};

int test_noc() {
    init_vnice();

    Tensor shape_tensor = (Tensor){
        .base_addr      = -1,
        .dim0           = 256,
        .dim1           = 32,
        .dim2           = 1,
        .byte_stride1_u = 32 * 16, 
        .byte_stride2_u = 32 * 512, 
        .width          = WIDTH_16,
        .type           = TYPE_BF 
    };


    int npu_mask[MAX_MASK] = {0x3, 0, 0, 0}; 
    noc_primitive_cfg(&shape_tensor, npu_mask);

    noc_primitive_src_drv(SCRATCHPAD0_ADDR, 0x0001, (int[]){0x1, 0, 0, 0});
    noc_primitive_dest_drv(SCRATCHPAD0_ADDR, 0x0000, (int[]){0x2, 0, 0, 0});


    return 0;
}