#include "minicpmv_attn.h"
#include "hardware_inst_data.h"
#include "high_level.h"
#include "minicpmv_basic.h"
#include "minicpmv_def.h"
#include <stdint.h>

// Tiling constants
#define TILE_N 10
#define TILE_D 64
#define TILE_C 128
#define MAX_HEADS_PER_GROUP 3





// ========================  主实现  =========================
void minicpmv2_qkvgen_prefill(int layer_id, const Tensor* X_dram, const Tensor* Q_out)
{
    // ---- 常量 ----
    const uint32_t NUM_GROUPS = MINICPMV2_NUM_GROUPS; // 4
    const uint32_t CORES_PER_GROUP = 4;
    const uint32_t NUM_CORES = MINICPMV2_NUM_NODES; // 16
    const uint32_t HEAD_DIM = MINICPMV2_EMBEDDING_DIM / 36; // 64
    const uint32_t BYTES_PER_ELEM = 2; // BF16
    const uint32_t ROW_BYTES_IN = MINICPMV2_EMBEDDING_DIM * BYTES_PER_ELEM; // 2304*2

    const uint32_t SPAD0_BASE = SCRATCHPAD0_ADDR; // X_slice
    const uint32_t SPAD1_BASE = SCRATCHPAD1_ADDR; // Q psum + rope_cos 缓存
    const uint32_t SPAD2_BASE = SCRATCHPAD2_ADDR; // K psum + rope_sin 缓存
    const uint32_t SPAD3_BASE = SCRATCHPAD3_ADDR; // V psum

    // Rope 半缓存常量
    const uint32_t ROPE_HALF_ROWS = 360; // 720 / 2
    const uint32_t ROPE_ROW_BYTES = HEAD_DIM * BYTES_PER_ELEM;
    const uint32_t ROPE_HALF_BYTES = ROPE_HALF_ROWS * ROPE_ROW_BYTES; // 45KB
    const uint32_t COS_CACHE_BASE = SPAD1_BASE + 64 * 1024 - ROPE_HALF_BYTES; // SPAD1 末尾
    const uint32_t SIN_CACHE_BASE = SPAD2_BASE + 64 * 1024 - ROPE_HALF_BYTES; // SPAD2 末尾
    // npu_core full mask
    int FULL_MASK[4] = { 0xf, 0xf, 0xf, 0xf };
    int npu_mask_group[4]; // group-level mask, declare early for RoPE loading
    Tensor src_cos, src_sin;
    Tensor dst_cos, dst_sin;

    // ---- 预加载前 360 行 rope cos/sin (按 group 单独搬运) ----
    for (uint32_t g = 0; g < NUM_GROUPS; ++g) {
        make_group_mask(g, npu_mask_group); // 仅激活该组 4 核

        build_tensor(minicpmv2_weight.group[g].rope.cos.addr_dram, ROPE_HALF_ROWS, HEAD_DIM, TYPE_BF, WIDTH_16, &src_cos);
        build_tensor(minicpmv2_weight.group[g].rope.sin.addr_dram, ROPE_HALF_ROWS, HEAD_DIM, TYPE_BF, WIDTH_16, &src_sin);

        build_tensor(COS_CACHE_BASE, ROPE_HALF_ROWS, HEAD_DIM, TYPE_BF, WIDTH_16, &dst_cos);
        build_tensor(SIN_CACHE_BASE, ROPE_HALF_ROWS, HEAD_DIM, TYPE_BF, WIDTH_16, &dst_sin);

        load(&src_cos, &dst_cos, npu_mask_group);
        load(&src_sin, &dst_sin, npu_mask_group);
    }

    // ---- batch 循环 ----
    const uint32_t NUM_BATCHES = X_dram->dim1 / TILE_N; // 72
    debug_assert(NUM_BATCHES * TILE_N == X_dram->dim1);

    int npu_mask_core[4]; // 单核 mask
    // int npu_mask_group declared above

    // Tensor 句柄
    Tensor X_slice_spad; // [10, 2304]
    Tensor X_tile_view; // [10, TILE_C]

    Tensor Q_psum, K_psum, V_psum; // [10, 64], 结果保存在 SPAD1/2/3 顶部

    // Prepare psum tensors (base addr fixed, rows 10, cols 64)
    build_tensor(SPAD1_BASE, TILE_N, HEAD_DIM, TYPE_BF, WIDTH_16, &Q_psum);
    build_tensor(SPAD2_BASE, TILE_N, HEAD_DIM, TYPE_BF, WIDTH_16, &K_psum);
    build_tensor(SPAD3_BASE, TILE_N, HEAD_DIM, TYPE_BF, WIDTH_16, &V_psum);

    // Cim option template
    CIM_Option cim_opt;
    cim_opt.type = TYPE_INT;
    cim_opt.width = WIDTH_4;
    cim_opt.activate = 0;

    for (uint32_t batch_idx = 0; batch_idx < NUM_BATCHES; ++batch_idx) {
        uint32_t row_start = batch_idx * TILE_N;

        // 切片 X → SPAD0
        slice_to_spad(X_dram, row_start, TILE_N, SPAD0_BASE, FULL_MASK, &X_slice_spad);

        // ---- Rope 后半重载判断 ----
        if (batch_idx == ROPE_HALF_ROWS / TILE_N) { // 36
            for (uint32_t g = 0; g < NUM_GROUPS; ++g) {
                make_group_mask(g, npu_mask_group);

                build_tensor(minicpmv2_weight.group[g].rope.cos.addr_dram + ROPE_HALF_BYTES, ROPE_HALF_ROWS, HEAD_DIM, TYPE_BF, WIDTH_16, &src_cos);
                build_tensor(minicpmv2_weight.group[g].rope.sin.addr_dram + ROPE_HALF_BYTES, ROPE_HALF_ROWS, HEAD_DIM, TYPE_BF, WIDTH_16, &src_sin);
                build_tensor(COS_CACHE_BASE, ROPE_HALF_ROWS, HEAD_DIM, TYPE_BF, WIDTH_16, &dst_cos);
                build_tensor(SIN_CACHE_BASE, ROPE_HALF_ROWS, HEAD_DIM, TYPE_BF, WIDTH_16, &dst_sin);

                load(&src_cos, &dst_cos, npu_mask_group);
                load(&src_sin, &dst_sin, npu_mask_group);
            }
        }

        // ---- head 循环 (至多 3) ----
        for (uint32_t head_iter = 0; head_iter < MAX_HEADS_PER_GROUP; ++head_iter) {
            // ---- group 循环 ----
            for (uint32_t group_id = 0; group_id < NUM_GROUPS; ++group_id) {
                uint32_t heads_per_core = minicpmv2_head_maping(group_id, layer_id);
                if (head_iter >= heads_per_core) {
                    continue; // 该组本轮无 head
                }

                // ---- 根据 group 生成 mask（core 层硬件并行） ----
                make_group_mask(group_id, npu_mask_group);

                // ======== 18 个 K tile 循环 ========
                for (uint32_t tile_k = 0; tile_k < MINICPMV2_EMBEDDING_DIM / TILE_C; ++tile_k) {
                    uint32_t k_start = tile_k * TILE_C; // 0,128,256...
                    uint32_t page_idx_q = (tile_k * 3) % 16; // 为 Q,K,V 分配不同 CIM 页
                    uint32_t page_idx_k = (tile_k * 3 + 1) % 16;
                    uint32_t page_idx_v = (tile_k * 3 + 2) % 16;

                    uint32_t cim_base_q = CIMC_PAGE_BASE_ADDR + page_idx_q * CIMC_PAGE_OFFSET;
                    uint32_t cim_base_k = CIMC_PAGE_BASE_ADDR + page_idx_k * CIMC_PAGE_OFFSET;
                    uint32_t cim_base_v = CIMC_PAGE_BASE_ADDR + page_idx_v * CIMC_PAGE_OFFSET;

                    // ---- 1. 取 X_tile 视图 [10, TILE_C]
                    make_tensor_view(&X_slice_spad, 0, k_start, TILE_N, TILE_C, &X_tile_view);

                    // ---- 2. 将 Weight tile 载入 CIM (按 group 地址) ----
                    const __WeightElem* wt_q = &minicpmv2_weight.group[group_id].layer[layer_id].self_attn.q_proj;
                    const __WeightElem* wt_k = &minicpmv2_weight.group[group_id].layer[layer_id].self_attn.k_proj;
                    const __WeightElem* wt_v = &minicpmv2_weight.group[group_id].layer[layer_id].self_attn.v_proj;

                    uint32_t wt_head_offset_rows = (head_iter * MINICPMV2_EMBEDDING_DIM) + k_start; // rows offset within weight
                    uint32_t wt_stride_row_qkv = width_enum_to_bits(WIDTH_4) * HEAD_DIM / 8;

                    Tensor wt_src_q;
                    build_tensor(wt_q->addr_dram + wt_head_offset_rows * wt_stride_row_qkv, TILE_C, HEAD_DIM, TYPE_INT, WIDTH_4, &wt_src_q);
                    Tensor wt_src_k;
                    build_tensor(wt_k->addr_dram + wt_head_offset_rows * wt_stride_row_qkv, TILE_C, HEAD_DIM, TYPE_INT, WIDTH_4, &wt_src_k);
                    Tensor wt_src_v;
                    build_tensor(wt_v->addr_dram + wt_head_offset_rows * wt_stride_row_qkv, TILE_C, HEAD_DIM, TYPE_INT, WIDTH_4, &wt_src_v);

                    Tensor wt_dst_q;
                    build_cimc_tensor(cim_base_q, TILE_C, HEAD_DIM, TYPE_INT, WIDTH_4, CIMC_ROW2COL2, &wt_dst_q);
                    Tensor wt_dst_k;
                    build_cimc_tensor(cim_base_k, TILE_C, HEAD_DIM, TYPE_INT, WIDTH_4, CIMC_ROW2COL2, &wt_dst_k);
                    Tensor wt_dst_v;
                    build_cimc_tensor(cim_base_v, TILE_C, HEAD_DIM, TYPE_INT, WIDTH_4, CIMC_ROW2COL2, &wt_dst_v);

                    load(&wt_src_q, &wt_dst_q, npu_mask_group);
                    load(&wt_src_k, &wt_dst_k, npu_mask_group);
                    load(&wt_src_v, &wt_dst_v, npu_mask_group);

                    // 3. gemm Q
                    cim_opt.page_index = page_idx_q;
                    cim_opt.accumulate = (tile_k == 0 ? 0 : 1);
                    gemm(&X_tile_view, &Q_psum, &Q_psum, &cim_opt, npu_mask_group);

                    // 4. gemm K
                    cim_opt.page_index = page_idx_k;
                    gemm(&X_tile_view, &K_psum, &K_psum, &cim_opt, npu_mask_group);

                    // 5. gemm V
                    cim_opt.page_index = page_idx_v;
                    gemm(&X_tile_view, &V_psum, &V_psum, &cim_opt, npu_mask_group);
                }

                // ======== GEMM 完毕，开始 RoPE & 写回 ========
                uint32_t rope_row_offset = row_start % ROPE_HALF_ROWS;
                Tensor sin_view, cos_view;
                build_tensor(SIN_CACHE_BASE + rope_row_offset * ROPE_ROW_BYTES, TILE_N, HEAD_DIM, TYPE_BF, WIDTH_16, &sin_view);
                build_tensor(COS_CACHE_BASE + rope_row_offset * ROPE_ROW_BYTES, TILE_N, HEAD_DIM, TYPE_BF, WIDTH_16, &cos_view);

                // =====  为 RoPE 准备 InterMemory (Q 在 SPAD1, K 在 SPAD2) =====
                VP_Option vp_null = { .special_case = { 0 }, .operation = OPERATION_NULL, .scalar_in2 = 0 };

                static InterMemory rope_mem_q_buf[4];
                static InterMemory rope_mem_k_buf[4];
                static int rope_mem_init = 0;
                if (!rope_mem_init) {
                    uint32_t base_q = SPAD1_BASE + 0x0800; // 2KB offset
                    uint32_t base_k = SPAD2_BASE + 0x0800;
                    for (int i = 0; i < 4; ++i) {
                        rope_mem_q_buf[i].base_addr = base_q + i * 0x0800; // 2KB chunk
                        rope_mem_q_buf[i].byte_size = 0x0800;
                        rope_mem_k_buf[i].base_addr = base_k + i * 0x0800;
                        rope_mem_k_buf[i].byte_size = 0x0800;
                    }
                    rope_mem_init = 1;
                }

                InterMemoryArray rope_mem_q = { .memory = rope_mem_q_buf, .length = 4 };
                InterMemoryArray rope_mem_k = { .memory = rope_mem_k_buf, .length = 4 };

                rope(&Q_psum, &Q_psum, &sin_view, &cos_view, &rope_mem_q, &vp_null, npu_mask_group);
                rope(&K_psum, &K_psum, &sin_view, &cos_view, &rope_mem_k, &vp_null, npu_mask_group);

                // ---- 写回 DDR ----
                uint32_t q_base = Q_out->base_addr + (head_iter * MINICPMV2_PROMPT_LEN + row_start) * HEAD_DIM * BYTES_PER_ELEM;
                Tensor q_dst;
                build_tensor(q_base, TILE_N, HEAD_DIM, TYPE_BF, WIDTH_16, &q_dst);
                store(&Q_psum, &q_dst, npu_mask_group);

                uint32_t head_slice_byte = head_slice_bytes();
                uint32_t kv_layer_base = minicpmv2_kcache.group[group_id][layer_id].addr_dram;
                uint32_t vv_layer_base = minicpmv2_vcache.group[group_id][layer_id].addr_dram;

                uint32_t k_dst_base = kv_layer_base + head_iter * head_slice_byte + row_start * HEAD_DIM * BYTES_PER_ELEM;
                uint32_t v_dst_base = vv_layer_base + head_iter * head_slice_byte + row_start * HEAD_DIM * BYTES_PER_ELEM;

                Tensor k_dst, v_dst;
                build_tensor(k_dst_base, TILE_N, HEAD_DIM, TYPE_BF, WIDTH_16, &k_dst);
                build_tensor(v_dst_base, TILE_N, HEAD_DIM, TYPE_BF, WIDTH_16, &v_dst);
                store(&K_psum, &k_dst, npu_mask_group);
                store(&V_psum, &v_dst, npu_mask_group);
            }
        }
    }
}



/// @brief MiniCPMV2 QKV Generation Prefill with INT4 Quantization Support (Simplified)
/// @param layer_id 层号, 0~39
/// @param X_dram 输入X(shape=[720,2304]), 储存在dram上  
/// @param Q_out Q_proj结果保存到q_out, shape=[heads_total*720, 64]
/// @param intermemory 中间计算缓存数组，需要18个块，约16KB
/// InterMemory布局：
/// [0]: scale参数缓存 [18, 64] BF16 = 2304B
/// [1]: zero参数缓存 [18, 64] INT4 = 576B  
/// [2]: Q量化累加器 [10, 64] BF16 = 1280B
/// [3]: K量化累加器 [10, 64] BF16 = 1280B
/// [4]: V量化累加器 [10, 64] BF16 = 1280B
/// [5-8]: RoPE Q中间缓存 (4个，每个128B)
/// [9-12]: RoPE K中间缓存 (4个，每个128B)
/// [13]: X_slice缓存 [10, 2304] BF16 = 46080B (SPAD0替代)
/// [14]: Q psum缓存 [10, 64] BF16 = 1280B (SPAD1替代)
/// [15]: K psum缓存 [10, 64] BF16 = 1280B (SPAD2替代)
/// [16]: V psum缓存 [10, 64] BF16 = 1280B (SPAD3替代)
/// [17]: RoPE cos缓存 [10, 64] BF16 = 1280B
/// [18]: RoPE sin缓存 [10, 64] BF16 = 1280B
void minicpmv2_qkvgen_prefill_quantized(
    int layer_id, 
    const Tensor* X_dram, 
    const Tensor* Q_out,
    InterMemoryArray* intermemory)
{
    // ---- 常量定义 ----
    const uint32_t NUM_CORES = MINICPMV2_NUM_NODES;        // 16
    const uint32_t HEAD_DIM = MINICPMV2_EMBEDDING_DIM / 36; // 64
    const uint32_t BYTES_PER_ELEM = 2;                      // BF16
    const uint32_t NUM_BATCHES = X_dram->dim1 / TILE_N;     // 72
    
    // ---- InterMemory验证 ----
    debug_assert(intermemory && intermemory->length >= 19);
    
    // ---- 预分配所有InterMemory地址变量 ----
    uint32_t scale_cache_addr = intermemory->memory[0].base_addr;
    uint32_t zero_cache_addr = intermemory->memory[1].base_addr;
    uint32_t quant_accum_q_addr = intermemory->memory[2].base_addr;
    uint32_t quant_accum_k_addr = intermemory->memory[3].base_addr;
    uint32_t quant_accum_v_addr = intermemory->memory[4].base_addr;
    uint32_t x_slice_addr = intermemory->memory[13].base_addr;
    uint32_t q_psum_addr = intermemory->memory[14].base_addr;
    uint32_t k_psum_addr = intermemory->memory[15].base_addr;
    uint32_t v_psum_addr = intermemory->memory[16].base_addr;
    uint32_t rope_cos_cache_addr = intermemory->memory[17].base_addr;
    uint32_t rope_sin_cache_addr = intermemory->memory[18].base_addr;
    
    // 全核mask
    int FULL_MASK[4] = { 0xf, 0xf, 0xf, 0xf };
    
    // Tensor句柄
    Tensor X_slice_cache;
    Tensor Q_psum, K_psum, V_psum;
    
    // 量化参数和累加器tensor
    Tensor scale_tensor, zero_tensor;
    Tensor quant_accumulator_q, quant_accumulator_k, quant_accumulator_v;
    
    // RoPE缓存tensors
    Tensor rope_cos_cache, rope_sin_cache;
    
    // 准备缓存tensors (使用InterMemory而非直接SPAD)
    build_tensor(x_slice_addr, TILE_N, MINICPMV2_EMBEDDING_DIM, TYPE_BF, WIDTH_16, &X_slice_cache);
    build_tensor(q_psum_addr, TILE_N, HEAD_DIM, TYPE_BF, WIDTH_16, &Q_psum);
    build_tensor(k_psum_addr, TILE_N, HEAD_DIM, TYPE_BF, WIDTH_16, &K_psum);
    build_tensor(v_psum_addr, TILE_N, HEAD_DIM, TYPE_BF, WIDTH_16, &V_psum);
    
    // 准备量化参数和累加器tensors
    build_tensor(scale_cache_addr, 18, HEAD_DIM, TYPE_BF, WIDTH_16, &scale_tensor);
    build_tensor(zero_cache_addr, 18, HEAD_DIM, TYPE_INT, WIDTH_4, &zero_tensor);
    build_tensor(quant_accum_q_addr, TILE_N, HEAD_DIM, TYPE_BF, WIDTH_16, &quant_accumulator_q);
    build_tensor(quant_accum_k_addr, TILE_N, HEAD_DIM, TYPE_BF, WIDTH_16, &quant_accumulator_k);
    build_tensor(quant_accum_v_addr, TILE_N, HEAD_DIM, TYPE_BF, WIDTH_16, &quant_accumulator_v);
    
    // 准备RoPE缓存tensors
    build_tensor(rope_cos_cache_addr, TILE_N, HEAD_DIM, TYPE_BF, WIDTH_16, &rope_cos_cache);
    build_tensor(rope_sin_cache_addr, TILE_N, HEAD_DIM, TYPE_BF, WIDTH_16, &rope_sin_cache);
    
    // ---- Batch循环 ----
    for (uint32_t batch_idx = 0; batch_idx < NUM_BATCHES; ++batch_idx) {
        uint32_t row_start = batch_idx * TILE_N;
        
        // 切片X → 缓存 (使用InterMemory而非直接SPAD)
        slice_to_spad(X_dram, row_start, TILE_N, x_slice_addr, FULL_MASK, &X_slice_cache);
        
        // ======== 加载当前batch的RoPE cos/sin数据到缓存 ========
        uint32_t rope_row_offset = row_start * HEAD_DIM * BYTES_PER_ELEM;
        
        // 遍历所有group，将各自的cos/sin加载到同一缓存地址
        for (uint32_t g = 0; g < MINICPMV2_NUM_GROUPS; ++g) {
            int group_mask[4];
            make_group_mask(g, group_mask);
            
            Tensor rope_cos_src, rope_sin_src;
            build_tensor(minicpmv2_weight.group[g].rope.cos.addr_dram + rope_row_offset,
                TILE_N, HEAD_DIM, TYPE_BF, WIDTH_16, &rope_cos_src);
            build_tensor(minicpmv2_weight.group[g].rope.sin.addr_dram + rope_row_offset,
                TILE_N, HEAD_DIM, TYPE_BF, WIDTH_16, &rope_sin_src);
            
            load(&rope_cos_src, &rope_cos_cache, group_mask);
            load(&rope_sin_src, &rope_sin_cache, group_mask);
        }
        
        // ======== Core级循环（简化版本） ========
        for (uint32_t core_idx = 0; core_idx < NUM_CORES; ++core_idx) {
            int group_id = core_idx / 4;
            int core_in_group = core_idx % 4;
            int heads_per_core = minicpmv2_head_maping(group_id, layer_id);
            
            // 单核mask
            int npu_mask_single[4];
            make_single_core_mask(core_idx, npu_mask_single);
            
            // ---- Head循环 ----
            for (uint32_t head_iter = 0; head_iter < heads_per_core; ++head_iter) {
                
                // ---- 1. 加载当前head的量化参数 ----
                uint32_t scale_head_offset = head_iter * 18 * HEAD_DIM * BYTES_PER_ELEM;
                uint32_t zero_head_offset = head_iter * 18 * HEAD_DIM * BYTES_PER_ELEM;
                
                // Q的scale/zero参数（Q/K/V共用一组参数）
                Tensor scale_src_q, zero_src_q;
                build_tensor(
                    minicpmv2_weight.group[group_id].layer[layer_id].self_attn.q_proj_scale.addr_dram + scale_head_offset,
                    18, HEAD_DIM, TYPE_BF, WIDTH_16, &scale_src_q);
                build_tensor(
                    minicpmv2_weight.group[group_id].layer[layer_id].self_attn.q_proj_zero.addr_dram + zero_head_offset,
                    18, HEAD_DIM, TYPE_INT, WIDTH_4, &zero_src_q);
                
                load(&scale_src_q, &scale_tensor, npu_mask_single);
                load(&zero_src_q, &zero_tensor, npu_mask_single);
                
                // ---- 2. 准备VP选项 ----
                VP_Option vp_sub = { .special_case = { 0 }, .operation = OPERATION_SUB, .scalar_in2 = 0 };
                VP_Option vp_mul = { .special_case = { 0 }, .operation = OPERATION_MUL, .scalar_in2 = 0 };
                VP_Option vp_add = { .special_case = { 0 }, .operation = OPERATION_ADD, .scalar_in2 = 0 };
                
                // CIM选项模板
                CIM_Option cim_opt;
                cim_opt.type = TYPE_INT;
                cim_opt.width = WIDTH_4;
                cim_opt.activate = 0;
                cim_opt.accumulate = 0;  // 每次tile都不累加，由我们手动反量化后累加
                
                // ---- 3. Tile循环（每个tile独立GEMM+反量化+累加） ----
                for (uint32_t tile_k = 0; tile_k < 18; tile_k++) {
                    uint32_t k_start = tile_k * TILE_C;  // 0, 128, 256, ...
                    
                    // 计算CIM页面索引（Q/K/V错开分配）
                    uint32_t page_idx_q = (tile_k * 3 + 0) % 16;
                    uint32_t page_idx_k = (tile_k * 3 + 1) % 16;
                    uint32_t page_idx_v = (tile_k * 3 + 2) % 16;
                    
                    uint32_t cim_base_q = CIMC_PAGE_BASE_ADDR + page_idx_q * CIMC_PAGE_OFFSET;
                    uint32_t cim_base_k = CIMC_PAGE_BASE_ADDR + page_idx_k * CIMC_PAGE_OFFSET;
                    uint32_t cim_base_v = CIMC_PAGE_BASE_ADDR + page_idx_v * CIMC_PAGE_OFFSET;
                    
                    // 取X_tile视图 [10, 128]
                    Tensor X_tile_view;
                    make_tensor_view(&X_slice_cache, 0, k_start, TILE_N, TILE_C, &X_tile_view);
                    
                    // 计算权重地址
                    uint32_t wt_stride_row = width_enum_to_bits(WIDTH_4) * HEAD_DIM / 8;  // INT4权重行步长
                    uint32_t wt_head_offset = (head_iter * MINICPMV2_EMBEDDING_DIM ) * wt_stride_row;
                    uint32_t wt_tile_offset = k_start * wt_stride_row;
                    // ---- 3.1 Q GEMM + 反量化 + 累加 ----
                    const __WeightElem* wt_q = &minicpmv2_weight.group[group_id].layer[layer_id].self_attn.q_proj;
                    Tensor wt_src_q, wt_dst_q;
                    build_tensor(wt_q->addr_dram + wt_head_offset + wt_tile_offset, 
                                TILE_C, HEAD_DIM, TYPE_INT, WIDTH_4, &wt_src_q);
                    build_cimc_tensor(cim_base_q, TILE_C, HEAD_DIM, TYPE_INT, WIDTH_4, CIMC_ROW2COL2, &wt_dst_q);
                    
                    load(&wt_src_q, &wt_dst_q, npu_mask_single);
                    cim_opt.page_index = page_idx_q;
                    gemm(&X_tile_view, &quant_accumulator_q, NULL, &cim_opt, npu_mask_single);
                    
                    // 反量化当前tile结果：用tile_k对应的scale/zero参数
                    Tensor scale_tile, zero_tile;
                    make_tensor_view(&scale_tensor, tile_k, 0, 1, HEAD_DIM, &scale_tile);  // [1, 64]
                    make_tensor_view(&zero_tensor, tile_k, 0, 1, HEAD_DIM, &zero_tile);    // [1, 64]
                    
                    // 反量化：deq_result = scale * (quant - zero)
                    for (uint32_t row = 0; row < TILE_N; row++) {
                        Tensor deq_row;
                        make_tensor_view(&quant_accumulator_q, row, 0, 1, HEAD_DIM, &deq_row);
                        // sub(&deq_row, &zero_tile, &deq_row, &vp_sub, npu_mask_single);
                        mul(&deq_row, &scale_tile, &deq_row, &vp_mul, npu_mask_single);
                    }
                    
                    // 累加到最终Q结果
                    if (tile_k == 0) {
                        // 第一次：先清零Q_psum，然后加上deq_result_q
                        sub(&Q_psum, &Q_psum, &Q_psum, &vp_sub, npu_mask_single);  // Q_psum = 0
                    }
                    add(&Q_psum, &quant_accumulator_q, &Q_psum, &vp_add, npu_mask_single);
                    
                    // ---- 3.2 K GEMM + 反量化 + 累加 ----
                    const __WeightElem* wt_k = &minicpmv2_weight.group[group_id].layer[layer_id].self_attn.k_proj;
                    Tensor wt_src_k, wt_dst_k;
                    build_tensor(wt_k->addr_dram + wt_head_offset + wt_tile_offset, 
                                TILE_C, HEAD_DIM, TYPE_INT, WIDTH_4, &wt_src_k);
                    build_cimc_tensor(cim_base_k, TILE_C, HEAD_DIM, TYPE_INT, WIDTH_4, CIMC_ROW2COL2, &wt_dst_k);
                    
                    load(&wt_src_k, &wt_dst_k, npu_mask_single);
                    cim_opt.page_index = page_idx_k;
                    gemm(&X_tile_view, &quant_accumulator_k, &quant_accumulator_k, &cim_opt, npu_mask_single);
                    
                    // K反量化+累加（复用deq_result_q的内存空间）
                    for (uint32_t row = 0; row < TILE_N; row++) {
                        Tensor deq_row;
                        make_tensor_view(&quant_accumulator_k, row, 0, 1, HEAD_DIM, &deq_row);
                        // sub(&deq_row, &zero_tile, &deq_row, &vp_sub, npu_mask_single);
                        mul(&deq_row, &scale_tile, &deq_row, &vp_mul, npu_mask_single);
                    }
                    // K累加到最终结果
                    if (tile_k == 0) {
                        sub(&K_psum, &K_psum, &K_psum, &vp_sub, npu_mask_single);  // K_psum = 0
                    }
                    add(&K_psum, &quant_accumulator_k, &K_psum, &vp_add, npu_mask_single);
                    
                    // ---- 3.3 V GEMM + 反量化 + 累加 ----
                    const __WeightElem* wt_v = &minicpmv2_weight.group[group_id].layer[layer_id].self_attn.v_proj;
                    Tensor wt_src_v, wt_dst_v;
                    build_tensor(wt_v->addr_dram + wt_head_offset + wt_tile_offset, 
                                TILE_C, HEAD_DIM, TYPE_INT, WIDTH_4, &wt_src_v);
                    build_cimc_tensor(cim_base_v, TILE_C, HEAD_DIM, TYPE_INT, WIDTH_4, CIMC_ROW2COL2, &wt_dst_v);
                    
                    load(&wt_src_v, &wt_dst_v, npu_mask_single);
                    cim_opt.page_index = page_idx_v;
                    gemm(&X_tile_view, &quant_accumulator_v, &quant_accumulator_v, &cim_opt, npu_mask_single);
                    
                    // V反量化+累加
                    for (uint32_t row = 0; row < TILE_N; row++) {
                        Tensor deq_row;
                        make_tensor_view(&quant_accumulator_v, row, 0, 1, HEAD_DIM, &deq_row);
                        // sub(&deq_row, &zero_tile, &deq_row, &vp_sub, npu_mask_single);
                        mul(&deq_row, &scale_tile, &deq_row, &vp_mul, npu_mask_single);
                    }
                    // V累加到最终结果
                    if (tile_k == 0) {
                        sub(&V_psum, &V_psum, &V_psum, &vp_sub, npu_mask_single);  // V_psum = 0
                    }
                    add(&V_psum, &quant_accumulator_v, &V_psum, &vp_add, npu_mask_single);
                }
                
                // ---- 6. RoPE处理（使用缓存中的数据） ----
                // RoPE InterMemory
                InterMemoryArray rope_mem_q = { .memory = &intermemory->memory[5], .length = 4 };
                InterMemoryArray rope_mem_k = { .memory = &intermemory->memory[9], .length = 4 };
                
                VP_Option vp_null = { .special_case = { 0 }, .operation = OPERATION_NULL, .scalar_in2 = 0 };
                
                // 应用RoPE（使用缓存中的cos/sin数据）
                rope(&Q_psum, &Q_psum, &rope_sin_cache, &rope_cos_cache, &rope_mem_q, &vp_null, npu_mask_single);
                rope(&K_psum, &K_psum, &rope_sin_cache, &rope_cos_cache, &rope_mem_k, &vp_null, npu_mask_single);
                
                // ---- 7. 写回DDR (修正：per_head [720,64]格式) ----
                // Q写回：每个head独立的[720,64]空间，按batch写入
                uint32_t q_base = Q_out->base_addr + (head_iter * MINICPMV2_PROMPT_LEN + row_start) * HEAD_DIM * BYTES_PER_ELEM;
                Tensor q_dst;
                build_tensor(q_base, TILE_N, HEAD_DIM, TYPE_BF, WIDTH_16, &q_dst);
                store(&Q_psum, &q_dst, npu_mask_single);
                
                // K/V写回到cache：每个core独立管理自己的K/V cache空间
                uint32_t kv_head_slice_bytes = head_slice_bytes();
                uint32_t kv_head_base = minicpmv2_kcache.group[group_id][layer_id].addr_dram + 
                                       head_iter * kv_head_slice_bytes;
                uint32_t vv_head_base = minicpmv2_vcache.group[group_id][layer_id].addr_dram + 
                                       head_iter * kv_head_slice_bytes;
                uint32_t kv_batch_offset = row_start * HEAD_DIM * BYTES_PER_ELEM;
                
                Tensor k_dst, v_dst;
                build_tensor(kv_head_base + kv_batch_offset, TILE_N, HEAD_DIM, TYPE_BF, WIDTH_16, &k_dst);
                build_tensor(vv_head_base + kv_batch_offset, TILE_N, HEAD_DIM, TYPE_BF, WIDTH_16, &v_dst);
                store(&K_psum, &k_dst, npu_mask_single);
                store(&V_psum, &v_dst, npu_mask_single);
            }
        }
    }
}

void minicpmv2_qkvgen_decode(int seq_idx, int layer_id, const Tensor* X_spad, const Tensor* q_out, const Tensor* k_out, const Tensor* v_out, InterMemoryArray* intermemory)
{
    // ---- 常量定义 ----
    const uint32_t NUM_GROUPS = MINICPMV2_NUM_GROUPS; // 4
    const uint32_t HEAD_DIM = MINICPMV2_EMBEDDING_DIM / 36; // 64
    const uint32_t BYTES_PER_ELEM = 2; // BF16

    // ---- InterMemory 分配验证 ----
    debug_assert(intermemory && intermemory->length >= 10);

    // InterMemory 布局:
    // [0]: cos 缓存 [1, 64] BF16 = 128B
    // [1]: sin 缓存 [1, 64] BF16 = 128B
    // [2-5]: Q RoPE 中间缓存 (4个，每个32B)
    // [6-9]: K RoPE 中间缓存 (4个，每个32B)
    uint32_t cos_cache_addr = intermemory->memory[0].base_addr;
    uint32_t sin_cache_addr = intermemory->memory[1].base_addr;

    InterMemoryArray rope_mem_q = { .memory = &intermemory->memory[2], .length = 4 };
    InterMemoryArray rope_mem_k = { .memory = &intermemory->memory[6], .length = 4 };

    // ---- X tile 视图和CIM选项准备 ----
    Tensor X_tile_view; // [1, TILE_C]
    CIM_Option cim_opt;
    cim_opt.type = TYPE_INT;
    cim_opt.width = WIDTH_4;
    cim_opt.activate = 0;

    // ======== 加载 RoPE cos/sin (移到group循环外，优化性能) ========
    // 简化的 RoPE 索引计算：seq_idx 直接计算偏移
    uint32_t rope_offset = seq_idx * HEAD_DIM * BYTES_PER_ELEM;

    // 构建目标缓存张量
    Tensor dst_cos, dst_sin;
    build_tensor(cos_cache_addr, 1, HEAD_DIM, TYPE_BF, WIDTH_16, &dst_cos);
    build_tensor(sin_cache_addr, 1, HEAD_DIM, TYPE_BF, WIDTH_16, &dst_sin);
    Tensor Q_psum, K_psum, V_psum;
    int npu_mask_group[4];
    int FULL_MASK[4] = { 0xf, 0xf, 0xf, 0xf };
    // 遍历所有group，使用group_mask将各自的cos/sin加载到同一缓存地址
    for (uint32_t g = 0; g < NUM_GROUPS; ++g) {
        int group_mask[4];
        make_group_mask(g, group_mask);

        Tensor src_cos, src_sin;
        build_tensor(minicpmv2_weight.group[g].rope.cos.addr_dram + rope_offset,
            1, HEAD_DIM, TYPE_BF, WIDTH_16, &src_cos);
        build_tensor(minicpmv2_weight.group[g].rope.sin.addr_dram + rope_offset,
            1, HEAD_DIM, TYPE_BF, WIDTH_16, &src_sin);

        load(&src_cos, &dst_cos, group_mask);
        load(&src_sin, &dst_sin, group_mask);
    }
    // ---- 主循环：Head 迭代 ----
    for (uint32_t head_iter = 0; head_iter < MAX_HEADS_PER_GROUP; ++head_iter) {

        // ---- Group 循环 ----
        for (uint32_t group_id = 0; group_id < NUM_GROUPS; ++group_id) {
            uint32_t heads_per_core = minicpmv2_head_maping(group_id, layer_id);
            if (head_iter >= heads_per_core) {
                continue; // 该组无当前 head
            }

            // ---- 生成 group mask ----
            make_group_mask(group_id, npu_mask_group);

            // ---- 使用输出地址作为 psum (避免使用SPAD空间) ----
            uint32_t head_offset_bytes = head_iter * HEAD_DIM * BYTES_PER_ELEM;
            build_tensor(q_out->base_addr + head_offset_bytes, 1, HEAD_DIM, TYPE_BF, WIDTH_16, &Q_psum);
            build_tensor(k_out->base_addr + head_offset_bytes, 1, HEAD_DIM, TYPE_BF, WIDTH_16, &K_psum);
            build_tensor(v_out->base_addr + head_offset_bytes, 1, HEAD_DIM, TYPE_BF, WIDTH_16, &V_psum);

            // ======== 18 个 tile_k 循环：GEMM 计算 ========
            for (uint32_t tile_k = 0; tile_k < MINICPMV2_EMBEDDING_DIM / TILE_C; ++tile_k) {
                uint32_t k_start = tile_k * TILE_C; // 0, 128, 256...
                uint32_t page_idx_q = (tile_k * 3) % 16;
                uint32_t page_idx_k = (tile_k * 3 + 1) % 16;
                uint32_t page_idx_v = (tile_k * 3 + 2) % 16;

                uint32_t cim_base_q = CIMC_PAGE_BASE_ADDR + page_idx_q * CIMC_PAGE_OFFSET;
                uint32_t cim_base_k = CIMC_PAGE_BASE_ADDR + page_idx_k * CIMC_PAGE_OFFSET;
                uint32_t cim_base_v = CIMC_PAGE_BASE_ADDR + page_idx_v * CIMC_PAGE_OFFSET;

                // ---- 1. 创建 X_tile 视图 [1, TILE_C] ----
                make_tensor_view(X_spad, 0, k_start, 1, TILE_C, &X_tile_view);

                // ---- 2. 加载权重到 CIM ----
                const __WeightElem* wt_q = &minicpmv2_weight.group[group_id].layer[layer_id].self_attn.q_proj;
                const __WeightElem* wt_k = &minicpmv2_weight.group[group_id].layer[layer_id].self_attn.k_proj;
                const __WeightElem* wt_v = &minicpmv2_weight.group[group_id].layer[layer_id].self_attn.v_proj;

                uint32_t wt_head_offset_rows = (head_iter * MINICPMV2_EMBEDDING_DIM) + k_start;
                uint32_t wt_stride_row_qkv = width_enum_to_bits(WIDTH_4) * HEAD_DIM / 8;

                Tensor wt_src_q, wt_src_k, wt_src_v;
                Tensor wt_dst_q, wt_dst_k, wt_dst_v;

                build_tensor(wt_q->addr_dram + wt_head_offset_rows * wt_stride_row_qkv,
                    TILE_C, HEAD_DIM, TYPE_INT, WIDTH_4, &wt_src_q);
                build_tensor(wt_k->addr_dram + wt_head_offset_rows * wt_stride_row_qkv,
                    TILE_C, HEAD_DIM, TYPE_INT, WIDTH_4, &wt_src_k);
                build_tensor(wt_v->addr_dram + wt_head_offset_rows * wt_stride_row_qkv,
                    TILE_C, HEAD_DIM, TYPE_INT, WIDTH_4, &wt_src_v);

                build_cimc_tensor(cim_base_q, TILE_C, HEAD_DIM, TYPE_INT, WIDTH_4, CIMC_ROW2COL2, &wt_dst_q);
                build_cimc_tensor(cim_base_k, TILE_C, HEAD_DIM, TYPE_INT, WIDTH_4, CIMC_ROW2COL2, &wt_dst_k);
                build_cimc_tensor(cim_base_v, TILE_C, HEAD_DIM, TYPE_INT, WIDTH_4, CIMC_ROW2COL2, &wt_dst_v);

                load(&wt_src_q, &wt_dst_q, npu_mask_group);
                load(&wt_src_k, &wt_dst_k, npu_mask_group);
                load(&wt_src_v, &wt_dst_v, npu_mask_group);

                // ---- 3. GEMM 计算 ----
                cim_opt.page_index = page_idx_q;
                cim_opt.accumulate = (tile_k == 0 ? 0 : 1);
                gemm(&X_tile_view, &Q_psum, &Q_psum, &cim_opt, npu_mask_group);

                cim_opt.page_index = page_idx_k;
                gemm(&X_tile_view, &K_psum, &K_psum, &cim_opt, npu_mask_group);

                cim_opt.page_index = page_idx_v;
                gemm(&X_tile_view, &V_psum, &V_psum, &cim_opt, npu_mask_group);
            }
        }
        // ======== 所有Group的GEMM完毕，进行RoPE处理 ========
        VP_Option vp_null = { .special_case = { 0 }, .operation = OPERATION_NULL, .scalar_in2 = 0 };
        int full_mask[4] = { 0xf, 0xf, 0xf, 0xf }; // 使用全部16核进行RoPE

        // Q 和 K 进行 RoPE，对完整的psum执行，使用full_mask
        rope(&Q_psum, &Q_psum, &dst_sin, &dst_cos, &rope_mem_q, &vp_null, full_mask);
        rope(&K_psum, &K_psum, &dst_sin, &dst_cos, &rope_mem_k, &vp_null, full_mask);

        // V 不需要 RoPE，GEMM结果已直接存储在输出地址
    }
}

void minicpmv2_flashattn_decode(int seq_idx,
    int layer_id,
    const Tensor* Q_spad, // [head_mapping, HEAD_DIM] 已在 SPAD
    const Tensor* O_out, // [heads_per_layer*1 , HEAD_DIM] 目标地址 (DRAM)
    InterMemoryArray* intermemory)
{
    // ==== 0. 常量与地址 ====
    const uint32_t NUM_CORES = 16; // 16 cores
    const uint32_t HEAD_DIM = MINICPMV2_EMBEDDING_DIM / 36; // 64
    const uint32_t BLOCK_M = TILE_N; // 10 (一块 key/value 行)
    const uint32_t BYTES_PER_ELEM = 2; // BF16
    const float INV_SQRT_D = 1.0f / 8.0f; // 1/sqrt(64) = 0.125

    // ---- InterMemory 分配验证 ----
    debug_assert(intermemory && intermemory->length >= 10);

    // InterMemory 布局:
    // [0]: K_block 缓存 (1280 bytes)
    // [1]: K_block_T 缓存 (1280 bytes)
    // [2]: V_block 缓存 (1280 bytes)
    // [3]: S_block/P_block 缓存 (复用, 32 bytes, 对齐)
    // [4]: O_accum 缓存 (128 bytes)
    // [5]: O_update 缓存 (128 bytes)
    // [6]: exp_v1 中间缓存 (1个)
    uint32_t k_block_addr = intermemory->memory[0].base_addr;
    uint32_t k_block_t_addr = intermemory->memory[1].base_addr;
    uint32_t v_block_addr = intermemory->memory[2].base_addr;
    uint32_t s_block_addr = intermemory->memory[3].base_addr;
    uint32_t o_accum_base_addr = intermemory->memory[4].base_addr;
    uint32_t o_update_addr = intermemory->memory[5].base_addr;

    // exp_v1 专用intermemory
    InterMemoryArray exp_intermemory = { .memory = &intermemory->memory[6], .length = 1 };

    // ---- 预生成常用 Tensor 句柄 ----
    Tensor Q_row; // [1, HEAD_DIM] 当前query
    Tensor S_block; // [1, BLOCK_M] attention scores
    Tensor P_block; // [1, BLOCK_M] probabilities after softmax
    Tensor K_block, K_block_T; // [BLOCK_M, HEAD_DIM] & [HEAD_DIM, BLOCK_M]
    Tensor V_block, V_block_T; // [BLOCK_M, HEAD_DIM] & [HEAD_DIM, BLOCK_M]
    Tensor O_accum; // [1, HEAD_DIM] 输出累积
    Tensor O_update; // [1, HEAD_DIM] 临时更新

    // 使用intermemory构建tensor
    build_tensor(s_block_addr, 1, BLOCK_M, TYPE_BF, WIDTH_16, &S_block);
    build_tensor(s_block_addr, 1, BLOCK_M, TYPE_BF, WIDTH_16, &P_block); // 复用地址
    build_tensor(o_update_addr, 1, HEAD_DIM, TYPE_BF, WIDTH_16, &O_update);

    VP_Option vp_null = { .special_case = { 0 }, .operation = OPERATION_NULL, .scalar_in2 = 0 };
    CIM_Option cim_opt = { .type = TYPE_BF, .width = WIDTH_16, .activate = 0 };

    // ====================== 主循环：head_iter → core_iter → block_iter ======================
    for (uint32_t head_iter = 0; head_iter < MAX_HEADS_PER_GROUP; ++head_iter) {
        uint32_t hist_rows = seq_idx + 1; // 历史长度

        // ====================  16-core 并行循环  ====================
        for (uint32_t core_idx = 0; core_idx < NUM_CORES; ++core_idx) {
            uint32_t group_id = core_idx / 4;
            uint32_t heads_per_core = minicpmv2_head_maping(group_id, layer_id);
            if (head_iter >= heads_per_core) {
                continue;
            }

            // ---- 生成单核mask ----
            int npu_mask_single[4];
            make_single_core_mask(core_idx, npu_mask_single);

            // ---- Running variables：直接用C标量变量！ ----
            float m_prev = -1e4f; // 初始最大值 = -∞
            float d_prev = 0.0f; // 初始分母 = 0

            // ---- 每个core使用独立地址空间的O_accum ----
            build_tensor(o_accum_base_addr, 1, HEAD_DIM, TYPE_BF, WIDTH_16, &O_accum);

            // ---- 初始化O_accum = 0 ----
            uint32_t zero_bf16 = float32_to_bf16(0.0f);
            broadcast(zero_bf16, &O_accum, npu_mask_single);

            // ---- 从 Q_spad 中获取当前 head 的 Q [1, 64] ----
            make_tensor_view(Q_spad, head_iter, 0, 1, HEAD_DIM, &Q_row);

            // ---- 构建当前 head 的 K/V cache Tensor ----
            uint32_t head_slice_byte = head_slice_bytes();
            uint32_t k_base = minicpmv2_kcache.group[group_id][layer_id].addr_dram + head_iter * head_slice_byte;
            uint32_t v_base = minicpmv2_vcache.group[group_id][layer_id].addr_dram + head_iter * head_slice_byte;
            Tensor K_cache_head, V_cache_head;
            build_tensor(k_base, hist_rows, HEAD_DIM, TYPE_BF, WIDTH_16, &K_cache_head);
            build_tensor(v_base, hist_rows, HEAD_DIM, TYPE_BF, WIDTH_16, &V_cache_head);

            // ====================  block_iter 循环：Online-Softmax  ====================
            for (uint32_t blk_start = 0; blk_start < hist_rows; blk_start += BLOCK_M) {
                uint32_t blk_rows = (blk_start + BLOCK_M <= hist_rows) ? BLOCK_M : (hist_rows - blk_start);

                // ---- 1. 取 K_block / V_block 到 intermemory ----
                build_tensor(k_block_addr, blk_rows, HEAD_DIM, TYPE_BF, WIDTH_16, &K_block);
                build_tensor(v_block_addr, blk_rows, HEAD_DIM, TYPE_BF, WIDTH_16, &V_block);
                slice_to_spad(&K_cache_head, blk_start, blk_rows, k_block_addr, npu_mask_single, &K_block);
                slice_to_spad(&V_cache_head, blk_start, blk_rows, v_block_addr, npu_mask_single, &V_block);

                // ---- 2. 转置 K_block → K_block_T (为GEMV准备) ----
                build_tensor(k_block_t_addr, HEAD_DIM, blk_rows, TYPE_BF, WIDTH_16, &K_block_T);
                transpose(&K_block, &K_block_T, npu_mask_single);

                // ---- 3. 计算 S_block = Q @ K_block_T  [1, blk_rows] ----
                // 重新构建S_block以匹配blk_rows
                build_tensor(s_block_addr, 1, blk_rows, TYPE_BF, WIDTH_16, &S_block);

                // 加载K到CIM
                uint32_t page_idx_k = 0;
                cim_opt.page_index = page_idx_k;
                uint32_t cim_base = CIMC_PAGE_BASE_ADDR + page_idx_k * CIMC_PAGE_OFFSET;
                Tensor wt_dst_k;
                build_cimc_tensor(cim_base, HEAD_DIM, blk_rows, TYPE_BF, WIDTH_16, CIMC_ROW2COL2, &wt_dst_k);
                load(&K_block_T, &wt_dst_k, npu_mask_single);

                // GEMV: Q_row @ K_block_T → S_block
                cim_opt.accumulate = 0;
                gemv(&Q_row, &S_block, NULL, &cim_opt, npu_mask_single);

                // ---- 4. Scaling: S_block *= 1/√d ----
                // 正确使用tensor_scalar_operator：标量在vp_option中
                VP_Option vp_scale = { .special_case = { 0 }, .operation = OPERATION_MUL, .scalar_in2 = float32_to_bf16(INV_SQRT_D) };
                mul_tensor_scalar(&S_block, &S_block, &S_block, &vp_scale, npu_mask_single);

                // ==================== Online-Softmax 更新 ====================

                // ---- 5. 计算当前block最大值 (标量) ----
                uint32_t max_current_bf16 = minicpmv2_reduce_max_single(&S_block, npu_mask_single);
                float max_current = bf16_to_float32(max_current_bf16);

                // ---- 6. Online-Softmax 标量更新 ----
                float m_new = fmaxf(m_prev, max_current); // m_new = max(m_prev, max_current)
                float r = expf(m_prev - m_new); // r = exp(m_prev - m_new)

                // ---- 7. 计算 P_block = exp(S_block - m_new) ----
                // 重新构建P_block以匹配blk_rows
                build_tensor(s_block_addr, 1, blk_rows, TYPE_BF, WIDTH_16, &P_block);

                // 正确使用：S_block - m_new，标量m_new在vp_option中
                VP_Option vp_sub = { .special_case = { 0 }, .operation = OPERATION_SUB, .scalar_in2 = float32_to_bf16(m_new) };
                sub_tensor_scalar(&S_block, &S_block, &P_block, &vp_sub, npu_mask_single);
                exp_v1(&P_block, &P_block, &vp_null, &exp_intermemory, npu_mask_single);

                // ---- 8. 计算当前block sum (标量) ----
                uint32_t sum_current_bf16 = minicpmv2_reduce_sum_single(&P_block, npu_mask_single);
                float sum_current = bf16_to_float32(sum_current_bf16);

                // ---- 9. 更新标量状态 ----
                float d_new = d_prev * r + sum_current; // d_new = d_prev * r + sum_current

                // ---- 10. 更新 O_accum: O_new = O_prev * r + P_block @ V_block ----
                // O_accum *= r (标量r在vp_option中)
                VP_Option vp_mul_r = { .special_case = { 0 }, .operation = OPERATION_MUL, .scalar_in2 = float32_to_bf16(r) };
                mul_tensor_scalar(&O_accum, &O_accum, &O_accum, &vp_mul_r, npu_mask_single);

                // P_block @ V_block：[1,blk_rows] @ [blk_rows,64] = [1,64]
                // V_block不需要转置，直接加载到CIM
                uint32_t page_idx_v = 1;
                cim_opt.page_index = page_idx_v;
                cim_base = CIMC_PAGE_BASE_ADDR + page_idx_v * CIMC_PAGE_OFFSET;
                Tensor wt_dst_v;
                build_cimc_tensor(cim_base, blk_rows, HEAD_DIM, TYPE_BF, WIDTH_16, CIMC_ROW2COL2, &wt_dst_v);
                load(&V_block, &wt_dst_v, npu_mask_single);

                gemv(&P_block, &O_update, &O_update, &cim_opt, npu_mask_single);

                // O_accum += O_update
                add(&O_accum, &O_update, &O_accum, &vp_null, npu_mask_single);

                // ---- 11. 更新状态变量（简单的C赋值！） ----
                m_prev = m_new; // 标量赋值
                d_prev = d_new; // 标量赋值

            } // ===== end for blk_start (block iteration) =====

            // ==================== 所有 block 处理完，最终归一化 ====================
            // O_final = O_accum / d_final
            VP_Option vp_div = { .special_case = { 0 }, .operation = OPERATION_MUL, .scalar_in2 = float32_to_bf16(1.0f / d_prev) };
            mul_tensor_scalar(&O_accum, &O_accum, &O_accum, &vp_div, npu_mask_single);

            // ---- 写回 DRAM ----
            uint32_t o_out_base = O_out->base_addr + head_iter * HEAD_DIM * BYTES_PER_ELEM;
            Tensor o_dst;
            build_tensor(o_out_base, 1, HEAD_DIM, TYPE_BF, WIDTH_16, &o_dst);
            store(&O_accum, &o_dst, npu_mask_single);

        } // ===== end for core_idx (16-core iteration) =====
    } // ===== end for head_iter =====
}

// ===================== 新设计：Token-by-Token Prefill (无需 Causal Mask) =====================

void minicpmv2_flashattn_prefill_tokenwise(int layer_id,
    const Tensor* Q_dram, // [prompt_len*head_mapping, HEAD_DIM] 在 DRAM
    const Tensor* O_out, // [prompt_len*head_mapping, HEAD_DIM] 目标地址 (DRAM)
    InterMemoryArray* intermemory)
{
    // ==== 0. 常量与地址 ====
    const uint32_t NUM_CORES = 16; // 16 cores
    const uint32_t HEAD_DIM = MINICPMV2_EMBEDDING_DIM / 36; // 64
    const uint32_t BYTES_PER_ELEM = 2; // BF16
    const float INV_SQRT_D = 1.0f / 8.0f; // 1/sqrt(64) = 0.125
    const uint32_t prompt_len = Q_dram->dim1; // prompt长度

    // ---- InterMemory 分配验证 ----
    debug_assert(intermemory && intermemory->length >= 7);

    // InterMemory 布局：
    // [0]: K_block 缓存 (1280 bytes)
    // [1]: K_block_T 缓存 (1280 bytes)
    // [2]: V_block 缓存 (1280 bytes)
    // [3]: S_block/P_block 缓存 (复用, 32 bytes)
    // [4]: O_accum 缓存 (128 bytes)
    // [5]: O_update 缓存 (128 bytes)
    // [6]: exp_v1 中间缓存 (1个)
    uint32_t k_block_addr = intermemory->memory[0].base_addr;
    uint32_t k_block_t_addr = intermemory->memory[1].base_addr;
    uint32_t v_block_addr = intermemory->memory[2].base_addr;
    uint32_t s_block_addr = intermemory->memory[3].base_addr;
    uint32_t o_accum_base_addr = intermemory->memory[4].base_addr;
    uint32_t o_update_addr = intermemory->memory[5].base_addr;

    // exp 中间缓存
    InterMemoryArray exp_intermemory = { .memory = &intermemory->memory[6], .length = 1 };

    // ---- 预生成常用 Tensor 句柄 ----
    Tensor Q_token; // [1, HEAD_DIM] 当前query token
    Tensor S_block; // [1, BLOCK_M] attention scores
    Tensor P_block; // [1, BLOCK_M] probabilities after softmax
    Tensor K_block, K_block_T; // [BLOCK_M, HEAD_DIM] & [HEAD_DIM, BLOCK_M]
    Tensor V_block; // [BLOCK_M, HEAD_DIM]
    Tensor O_accum; // [1, HEAD_DIM] 输出累积
    Tensor O_update; // [1, HEAD_DIM] 临时更新

    // 使用intermemory构建tensor
    build_tensor(s_block_addr, 1, TILE_N, TYPE_BF, WIDTH_16, &S_block);
    build_tensor(s_block_addr, 1, TILE_N, TYPE_BF, WIDTH_16, &P_block); // 复用地址
    build_tensor(o_update_addr, 1, HEAD_DIM, TYPE_BF, WIDTH_16, &O_update);

    VP_Option vp_null = { .special_case = { 0 }, .operation = OPERATION_NULL, .scalar_in2 = 0 };
    CIM_Option cim_opt = { .type = TYPE_BF, .width = WIDTH_16, .activate = 0 };

    // ========== 外层循环：逐个处理每个 Query Token (实现因果性约束) ==========
    for (uint32_t q_idx = 0; q_idx < prompt_len; ++q_idx) {

        // ---- 获取当前 Query Token ----
        make_tensor_view(Q_dram, q_idx, 0, 1, HEAD_DIM, &Q_token);

        // ---- 计算当前可见的历史长度 (因果性：只能看到 t <= current_t 的tokens) ----
        uint32_t visible_kv_len = q_idx + 1; // 包括当前 token

        // =============== 内层循环：head_iter → core_iter → block_iter (基于 Online-Softmax) ===============
        for (uint32_t head_iter = 0; head_iter < MAX_HEADS_PER_GROUP; ++head_iter) {

            // ====================  16-core 并行循环  ====================
            for (uint32_t core_idx = 0; core_idx < NUM_CORES; ++core_idx) {
                uint32_t group_id = core_idx / 4;
                uint32_t heads_per_core = minicpmv2_head_maping(group_id, layer_id);
                if (head_iter >= heads_per_core) {
                    continue;
                }

                // ---- 生成单核mask ----
                int npu_mask_single[4];
                make_single_core_mask(core_idx, npu_mask_single);

                // ---- Online-Softmax 状态变量 (Flash Attention 核心算法) ----
                float m_prev = -1e4f; // 行最大值状态 m_i^{(j-1)}
                float d_prev = 0.0f; // 行分母状态 d_i^{(j-1)}

                // ---- 每个core使用独立地址空间的O_accum ----
                build_tensor(o_accum_base_addr, 1, HEAD_DIM, TYPE_BF, WIDTH_16, &O_accum);

                // ---- 初始化 O_accum = 0 (公式: O_i^{(0)} = 0) ----
                uint32_t zero_bf16 = float32_to_bf16(0.0f);
                broadcast(zero_bf16, &O_accum, npu_mask_single);

                // ---- 构建当前 head 的 K/V cache Tensor ----
                uint32_t head_slice_byte = head_slice_bytes();
                uint32_t k_base = minicpmv2_kcache.group[group_id][layer_id].addr_dram + head_iter * head_slice_byte;
                uint32_t v_base = minicpmv2_vcache.group[group_id][layer_id].addr_dram + head_iter * head_slice_byte;
                Tensor K_cache_head, V_cache_head;
                build_tensor(k_base, visible_kv_len, HEAD_DIM, TYPE_BF, WIDTH_16, &K_cache_head);
                build_tensor(v_base, visible_kv_len, HEAD_DIM, TYPE_BF, WIDTH_16, &V_cache_head);

                // ====================  block_iter 循环：Flash Attention Online-Softmax 算法 ====================
                const uint32_t BLOCK_M = TILE_N; // 10
                for (uint32_t blk_start = 0; blk_start < visible_kv_len; blk_start += BLOCK_M) {
                    uint32_t blk_rows = (blk_start + BLOCK_M <= visible_kv_len) ? BLOCK_M : (visible_kv_len - blk_start);

                    // ---- 1. 从 KV cache 加载 K_block / V_block 到 intermemory ----
                    build_tensor(k_block_addr, blk_rows, HEAD_DIM, TYPE_BF, WIDTH_16, &K_block);
                    build_tensor(v_block_addr, blk_rows, HEAD_DIM, TYPE_BF, WIDTH_16, &V_block);
                    slice_to_spad(&K_cache_head, blk_start, blk_rows, k_block_addr, npu_mask_single, &K_block);
                    slice_to_spad(&V_cache_head, blk_start, blk_rows, v_block_addr, npu_mask_single, &V_block);

                    // ---- 2. 转置 K_block → K_block_T (为 GEMV 计算 q·k^T 准备) ----
                    build_tensor(k_block_t_addr, HEAD_DIM, blk_rows, TYPE_BF, WIDTH_16, &K_block_T);
                    transpose(&K_block, &K_block_T, npu_mask_single);

                    // ---- 3. 计算 attention scores: S_ij = Q_i @ K_j^T ----
                    build_tensor(s_block_addr, 1, blk_rows, TYPE_BF, WIDTH_16, &S_block);

                    uint32_t page_idx_k = 0;
                    cim_opt.page_index = page_idx_k;
                    uint32_t cim_base = CIMC_PAGE_BASE_ADDR + page_idx_k * CIMC_PAGE_OFFSET;
                    Tensor wt_dst_k;
                    build_cimc_tensor(cim_base, HEAD_DIM, blk_rows, TYPE_BF, WIDTH_16, CIMC_ROW2COL2, &wt_dst_k);
                    load(&K_block_T, &wt_dst_k, npu_mask_single);

                    cim_opt.accumulate = 0;
                    gemv(&Q_token, &S_block, NULL, &cim_opt, npu_mask_single);

                    // ---- 4. 缩放: S_ij *= 1/√d_k (避免梯度消失) ----
                    VP_Option vp_scale = { .special_case = { 0 }, .operation = OPERATION_MUL, .scalar_in2 = float32_to_bf16(INV_SQRT_D) };
                    mul_tensor_scalar(&S_block, &S_block, &S_block, &vp_scale, npu_mask_single);

                    // ========== 天然满足因果性：q_idx >= blk_start..blk_end，无需显式 Causal Mask ==========

                    // ---- 5-11. Flash Attention Online-Softmax 更新公式 ----

                    // 5. 计算当前 block 的行最大值: m_i^{(j)} = max(S_ij)
                    uint32_t max_current_bf16 = minicpmv2_reduce_max_single(&S_block, npu_mask_single);
                    float max_current = bf16_to_float32(max_current_bf16);

                    // 6. 更新全局最大值: m_i^{new} = max(m_i^{(j-1)}, m_i^{(j)})
                    float m_new = fmaxf(m_prev, max_current);

                    // 7. 计算重标准化因子: r = exp(m_i^{(j-1)} - m_i^{new})
                    float r = expf(m_prev - m_new);

                    // 8. 计算当前 block 的 softmax: P_ij = exp(S_ij - m_i^{new})
                    build_tensor(s_block_addr, 1, blk_rows, TYPE_BF, WIDTH_16, &P_block);
                    VP_Option vp_sub = { .special_case = { 0 }, .operation = OPERATION_SUB, .scalar_in2 = float32_to_bf16(m_new) };
                    sub_tensor_scalar(&S_block, &S_block, &P_block, &vp_sub, npu_mask_single);
                    exp_v1(&P_block, &P_block, &vp_null, &exp_intermemory, npu_mask_single);

                    // 9. 计算当前 block 的 softmax 分母: l_i^{(j)} = sum(P_ij)
                    uint32_t sum_current_bf16 = minicpmv2_reduce_sum_single(&P_block, npu_mask_single);
                    float sum_current = bf16_to_float32(sum_current_bf16);

                    // 10. 更新全局分母: d_i^{new} = d_i^{(j-1)} * r + l_i^{(j)}
                    float d_new = d_prev * r + sum_current;

                    // 11. 更新输出累积: O_i^{new} = O_i^{(j-1)} * r + P_ij @ V_j
                    VP_Option vp_mul_r = { .special_case = { 0 }, .operation = OPERATION_MUL, .scalar_in2 = float32_to_bf16(r) };
                    mul_tensor_scalar(&O_accum, &O_accum, &O_accum, &vp_mul_r, npu_mask_single);

                    // P_block @ V_block → O_update: 加权值累积
                    uint32_t page_idx_v = 1;
                    cim_opt.page_index = page_idx_v;
                    cim_base = CIMC_PAGE_BASE_ADDR + page_idx_v * CIMC_PAGE_OFFSET;
                    Tensor wt_dst_v;
                    build_cimc_tensor(cim_base, blk_rows, HEAD_DIM, TYPE_BF, WIDTH_16, CIMC_ROW2COL2, &wt_dst_v);
                    load(&V_block, &wt_dst_v, npu_mask_single);

                    cim_opt.accumulate = 0;
                    gemv(&P_block, &O_update, &O_update, &cim_opt, npu_mask_single);
                    add(&O_accum, &O_update, &O_accum, &vp_null, npu_mask_single);

                    // 12. 更新状态变量
                    m_prev = m_new;
                    d_prev = d_new;

                } // ===== end for blk_start (block iteration) =====

                // ==================== 最终归一化: O_i^{final} = O_i^{accum} / d_i^{final} ====================
                VP_Option vp_div = { .special_case = { 0 }, .operation = OPERATION_MUL, .scalar_in2 = float32_to_bf16(1.0f / d_prev) };
                mul_tensor_scalar(&O_accum, &O_accum, &O_accum, &vp_div, npu_mask_single);

                // ---- 写回 DRAM (修正地址计算) ----
                // 每个核的输出布局: [head_mapping*prompt_len, HEAD_DIM]
                // 地址计算: head_iter * prompt_len * HEAD_DIM * BYTES_PER_ELEM + q_idx * HEAD_DIM * BYTES_PER_ELEM

                uint32_t token_offset = q_idx * HEAD_DIM * BYTES_PER_ELEM;
                uint32_t head_offset = head_iter * prompt_len * HEAD_DIM * BYTES_PER_ELEM;

                uint32_t o_out_base = O_out->base_addr + head_offset + token_offset;
                Tensor o_dst;
                build_tensor(o_out_base, 1, HEAD_DIM, TYPE_BF, WIDTH_16, &o_dst);
                store(&O_accum, &o_dst, npu_mask_single);

            } // ===== end for core_idx (16-core iteration) =====
        } // ===== end for head_iter =====
    } // ===== end for q_idx (token iteration) =====
}



void minicpmv2_output_gen_prefill(int layer_id, const Tensor* attn_out, const Tensor* output_final, InterMemoryArray* intermemory)
{
    // ---- 常量定义 ----
    const uint32_t NUM_CORES = MINICPMV2_NUM_NODES; // 16
    const uint32_t HEAD_DIM = MINICPMV2_EMBEDDING_DIM / 36; // 64
    const uint32_t OUTPUT_DIM = MINICPMV2_EMBEDDING_DIM; // 2304
    const uint32_t BYTES_PER_ELEM = 2; // BF16
    const uint32_t NUM_TILE_K = OUTPUT_DIM / TILE_C; // 18

    // ---- InterMemory 分配验证 ----
    debug_assert(intermemory && intermemory->length >= 4);

    // InterMemory 布局:
    // [0]: 单head输入缓存 [TILE_N, 64] (1280 bytes)
    // [1]: 双head输入重构缓存 [TILE_N, 128] (2560 bytes)
    // [2]: head_accumulator 缓存 [TILE_N, OUTPUT_DIM] (9216 bytes)
    // [3]: final_accumulator 缓存 [TILE_N, OUTPUT_DIM] (9216 bytes) - 用于3个head的累加
    uint32_t single_input_addr = intermemory->memory[0].base_addr;
    uint32_t dual_input_addr = intermemory->memory[1].base_addr;
    uint32_t head_accum_addr = intermemory->memory[2].base_addr;
    uint32_t final_accum_addr = intermemory->memory[3].base_addr;

    // ---- batch 循环 ----
    const uint32_t NUM_BATCHES = attn_out->dim1 / TILE_N; // prompt_len / TILE_N
    debug_assert(NUM_BATCHES * TILE_N == attn_out->dim1);

    int npu_mask_single[4]; // 单核mask
    int FULL_MASK[4] = { 0xf, 0xf, 0xf, 0xf };

    // Tensor 句柄
    Tensor input_single_view, input_dual_view, head_accumulator;
    Tensor weight_view, weight_dst, output_batch_dst;

    // CIM选项
    CIM_Option cim_opt;
    cim_opt.type = TYPE_INT;
    cim_opt.width = WIDTH_4;
    cim_opt.activate = 0;

    VP_Option vp_null = { .special_case = { 0 }, .operation = OPERATION_NULL, .scalar_in2 = 0 };

    for (uint32_t batch_idx = 0; batch_idx < NUM_BATCHES; ++batch_idx) {
        uint32_t row_start = batch_idx * TILE_N;

        // ---- core 循环 ----
        for (uint32_t core_idx = 0; core_idx < NUM_CORES; ++core_idx) {
            uint32_t group_id = core_idx / 4;
            uint32_t heads_per_core = minicpmv2_head_maping(group_id, layer_id);

            // ---- 生成单核mask ----
            make_single_core_mask(core_idx, npu_mask_single);

            // ---- 构建完整权重、scale、zero_point tensors ----
            const __WeightElem* wt_o = &minicpmv2_weight.group[group_id].layer[layer_id].self_attn.o_proj;
            const __WeightElem* wt_scale = &minicpmv2_weight.group[group_id].layer[layer_id].self_attn.o_proj_scale;
            const __WeightElem* wt_zero = &minicpmv2_weight.group[group_id].layer[layer_id].self_attn.o_proj_zero;

            Tensor weight_full;
            build_tensor(wt_o->addr_dram, OUTPUT_DIM, OUTPUT_DIM, TYPE_INT, WIDTH_4, &weight_full);

            // 计算当前core有多少组scale/zero_point (2个head=1组, 3个head=2组)
            uint32_t num_scale_groups = (heads_per_core == 2) ? 1 : 2;
            Tensor scale_full, zero_full;
            build_tensor(wt_scale->addr_dram, num_scale_groups, OUTPUT_DIM, TYPE_BF, WIDTH_16, &scale_full);
            build_tensor(wt_zero->addr_dram, num_scale_groups, OUTPUT_DIM, TYPE_INT, WIDTH_4, &zero_full);

            // ---- 初始化final累加器 [TILE_N, OUTPUT_DIM] ----
            Tensor final_accumulator;
            build_tensor(final_accum_addr, TILE_N, OUTPUT_DIM, TYPE_BF, WIDTH_16, &final_accumulator);
            uint32_t zero_bf16 = float32_to_bf16(0.0f);
            broadcast(zero_bf16, &final_accumulator, npu_mask_single);

            // ---- 根据head数量选择处理策略 ----
            if (heads_per_core == 2) {
                // ======== 2个head：直接双head优化 ========

                // 初始化head_accumulator
                build_tensor(head_accum_addr, TILE_N, OUTPUT_DIM, TYPE_BF, WIDTH_16, &head_accumulator);
                broadcast(zero_bf16, &head_accumulator, npu_mask_single);

                // 构建双head输入
                uint32_t head0_start_row = row_start;
                uint32_t head1_start_row = row_start + MINICPMV2_PROMPT_LEN;

                Tensor head0_view, head1_view;
                make_tensor_view(attn_out, head0_start_row, 0, TILE_N, HEAD_DIM, &head0_view);
                make_tensor_view(attn_out, head1_start_row, 0, TILE_N, HEAD_DIM, &head1_view);

                build_tensor(dual_input_addr, TILE_N, 128, TYPE_BF, WIDTH_16, &input_dual_view);
                Tensor output_view0, output_view1;
                make_tensor_view(&input_dual_view, 0, 0, TILE_N, HEAD_DIM, &output_view0);
                make_tensor_view(&input_dual_view, 0, HEAD_DIM, TILE_N, HEAD_DIM, &output_view1);

                load(&head0_view, &output_view0, npu_mask_single);
                load(&head1_view, &output_view1, npu_mask_single);

                // tile_k循环：双head GEMM
                for (uint32_t tile_k = 0; tile_k < NUM_TILE_K; ++tile_k) {
                    uint32_t k_start = tile_k * TILE_C;
                    uint32_t page_idx = tile_k % 16;
                    uint32_t cim_base = CIMC_PAGE_BASE_ADDR + page_idx * CIMC_PAGE_OFFSET;

                    // 权重view [128, 128]
                    uint32_t weight_start_row = 0; // 前两个head
                    make_tensor_view(&weight_full, weight_start_row, k_start, 128, TILE_C, &weight_view);
                    build_cimc_tensor(cim_base, 128, TILE_C, TYPE_INT, WIDTH_4, CIMC_ROW2COL2, &weight_dst);
                    load(&weight_view, &weight_dst, npu_mask_single);

                    // 输出到head_accumulator
                    Tensor output_view;
                    make_tensor_view(&head_accumulator, 0, k_start, TILE_N, TILE_C, &output_view);

                    cim_opt.page_index = page_idx;
                    cim_opt.accumulate = 0;
                    gemm(&input_dual_view, &output_view, &output_view, &cim_opt, npu_mask_single);
                }

                // 反量化：使用scale_index=0 (2个head只有1组)
                Tensor scale_view, zero_view;
                make_tensor_view(&scale_full, 0, 0, 1, OUTPUT_DIM, &scale_view); // [1, 2304]
                make_tensor_view(&zero_full, 0, 0, 1, OUTPUT_DIM, &zero_view); // [1, 2304]
                apply_dequantization(&head_accumulator, &scale_view, &zero_view, &final_accumulator, npu_mask_single);

            } else if (heads_per_core == 3) {
                // ======== 3个head：分两次处理，然后累加 ========

                // 第一次：双head优化
                build_tensor(head_accum_addr, TILE_N, OUTPUT_DIM, TYPE_BF, WIDTH_16, &head_accumulator);
                broadcast(zero_bf16, &head_accumulator, npu_mask_single);

                int pair_type = get_head_pair_type(layer_id, core_idx);
                uint32_t first_head_idx = (pair_type == 0) ? 0 : 1;
                uint32_t second_head_idx = first_head_idx + 1;

                uint32_t head0_start_row = row_start + first_head_idx * MINICPMV2_PROMPT_LEN;
                uint32_t head1_start_row = row_start + second_head_idx * MINICPMV2_PROMPT_LEN;

                Tensor head0_view, head1_view;
                make_tensor_view(attn_out, head0_start_row, 0, TILE_N, HEAD_DIM, &head0_view);
                make_tensor_view(attn_out, head1_start_row, 0, TILE_N, HEAD_DIM, &head1_view);

                build_tensor(dual_input_addr, TILE_N, 128, TYPE_BF, WIDTH_16, &input_dual_view);
                Tensor output_view0, output_view1;
                make_tensor_view(&input_dual_view, 0, 0, TILE_N, HEAD_DIM, &output_view0);
                make_tensor_view(&input_dual_view, 0, HEAD_DIM, TILE_N, HEAD_DIM, &output_view1);

                load(&head0_view, &output_view0, npu_mask_single);
                load(&head1_view, &output_view1, npu_mask_single);

                // 双head GEMM
                for (uint32_t tile_k = 0; tile_k < NUM_TILE_K; ++tile_k) {
                    uint32_t k_start = tile_k * TILE_C;
                    uint32_t page_idx = tile_k % 16;
                    uint32_t cim_base = CIMC_PAGE_BASE_ADDR + page_idx * CIMC_PAGE_OFFSET;

                    uint32_t weight_start_row = first_head_idx * HEAD_DIM;
                    make_tensor_view(&weight_full, weight_start_row, k_start, 128, TILE_C, &weight_view);
                    build_cimc_tensor(cim_base, 128, TILE_C, TYPE_INT, WIDTH_4, CIMC_ROW2COL2, &weight_dst);
                    load(&weight_view, &weight_dst, npu_mask_single);

                    Tensor output_view;
                    make_tensor_view(&head_accumulator, 0, k_start, TILE_N, TILE_C, &output_view);

                    cim_opt.page_index = page_idx;
                    cim_opt.accumulate = 0;
                    gemm(&input_dual_view, &output_view, &output_view, &cim_opt, npu_mask_single);
                }

                // 第一次反量化并累加到final_accumulator
                int scale_index1 = get_scale_index(layer_id, core_idx, first_head_idx, 1);
                Tensor scale_view1, zero_view1;
                make_tensor_view(&scale_full, scale_index1, 0, 1, OUTPUT_DIM, &scale_view1); // [1, 2304]
                make_tensor_view(&zero_full, scale_index1, 0, 1, OUTPUT_DIM, &zero_view1); // [1, 2304]
                apply_dequantization(&head_accumulator, &scale_view1, &zero_view1, &head_accumulator, npu_mask_single);
                add(&final_accumulator, &head_accumulator, &final_accumulator, &vp_null, npu_mask_single);

                // 第二次：单head处理
                broadcast(zero_bf16, &head_accumulator, npu_mask_single);

                uint32_t single_head_idx = (pair_type == 0) ? 2 : 0;
                uint32_t single_head_start_row = row_start + single_head_idx * MINICPMV2_PROMPT_LEN;

                make_tensor_view(attn_out, single_head_start_row, 0, TILE_N, HEAD_DIM, &input_single_view);
                Tensor spad_input;
                build_tensor(single_input_addr, TILE_N, HEAD_DIM, TYPE_BF, WIDTH_16, &spad_input);
                load(&input_single_view, &spad_input, npu_mask_single);

                // 单head GEMV
                for (uint32_t tile_k = 0; tile_k < NUM_TILE_K; ++tile_k) {
                    uint32_t k_start = tile_k * TILE_C;
                    uint32_t page_idx = tile_k % 16;
                    uint32_t cim_base = CIMC_PAGE_BASE_ADDR + page_idx * CIMC_PAGE_OFFSET;

                    uint32_t weight_start_row = single_head_idx * HEAD_DIM;
                    make_tensor_view(&weight_full, weight_start_row, k_start, HEAD_DIM, TILE_C, &weight_view);
                    build_cimc_tensor(cim_base, HEAD_DIM, TILE_C, TYPE_INT, WIDTH_4, CIMC_ROW2COL2, &weight_dst);
                    load(&weight_view, &weight_dst, npu_mask_single);

                    Tensor output_view;
                    make_tensor_view(&head_accumulator, 0, k_start, TILE_N, TILE_C, &output_view);

                    cim_opt.page_index = page_idx;
                    cim_opt.accumulate = 0;
                    gemm(&spad_input, &output_view, &output_view, &cim_opt, npu_mask_single);
                }

                // 第二次反量化并累加到final_accumulator
                int scale_index2 = get_scale_index(layer_id, core_idx, single_head_idx, 0);
                Tensor scale_view2, zero_view2;
                make_tensor_view(&scale_full, scale_index2, 0, 1, OUTPUT_DIM, &scale_view2); // [1, 2304]
                make_tensor_view(&zero_full, scale_index2, 0, 1, OUTPUT_DIM, &zero_view2); // [1, 2304]
                apply_dequantization(&head_accumulator, &scale_view2, &zero_view2, &head_accumulator, npu_mask_single);
                add(&final_accumulator, &head_accumulator, &final_accumulator, &vp_null, npu_mask_single);
            }

            // ---- 写回最终结果到DDR ----
            uint32_t output_offset = row_start * OUTPUT_DIM * BYTES_PER_ELEM;
            build_tensor(output_final->base_addr + output_offset, TILE_N, OUTPUT_DIM, TYPE_BF, WIDTH_16, &output_batch_dst);
            store(&final_accumulator, &output_batch_dst, npu_mask_single);

        } // ===== end core_idx =====
    } // ===== end batch_idx =====
}

void minicpmv2_output_gen_decode(int layer_id, const Tensor* attn_out, const Tensor* output_final, InterMemoryArray* intermemory)
{
    // ---- 常量定义 ----
    const uint32_t NUM_CORES = MINICPMV2_NUM_NODES; // 16
    const uint32_t HEAD_DIM = MINICPMV2_EMBEDDING_DIM / 36; // 64
    const uint32_t OUTPUT_DIM = MINICPMV2_EMBEDDING_DIM; // 2304
    const uint32_t BYTES_PER_ELEM = 2; // BF16
    const uint32_t NUM_TILE_K = OUTPUT_DIM / TILE_C; // 18

    // ---- InterMemory 分配验证 ----
    debug_assert(intermemory && intermemory->length >= 4);

    // InterMemory 布局:
    // [0]: 单head输入缓存 [1, 64] (128 bytes)
    // [1]: 双head输入重构缓存 [1, 128] (256 bytes)
    // [2]: head_accumulator 缓存 [1, OUTPUT_DIM] (4608 bytes)
    // [3]: final_accumulator 缓存 [1, OUTPUT_DIM] (4608 bytes) - 用于3个head的累加
    uint32_t single_input_addr = intermemory->memory[0].base_addr;
    uint32_t dual_input_addr = intermemory->memory[1].base_addr;
    uint32_t head_accum_addr = intermemory->memory[2].base_addr;
    uint32_t final_accum_addr = intermemory->memory[3].base_addr;

    int npu_mask_single[4]; // 单核mask
    int FULL_MASK[4] = { 0xf, 0xf, 0xf, 0xf };

    // Tensor 句柄
    Tensor input_single_view, input_dual_view, head_accumulator, final_accumulator;
    Tensor weight_view, weight_dst;

    // CIM选项
    CIM_Option cim_opt;
    cim_opt.type = TYPE_INT;
    cim_opt.width = WIDTH_4;
    cim_opt.activate = 0;

    VP_Option vp_null = { .special_case = { 0 }, .operation = OPERATION_NULL, .scalar_in2 = 0 };

    // ---- 初始化最终输出累加器为0 ----
    build_tensor(output_final->base_addr, 1, OUTPUT_DIM, TYPE_BF, WIDTH_16, &final_accumulator);
    uint32_t zero_bf16 = float32_to_bf16(0.0f);
    broadcast(zero_bf16, &final_accumulator, FULL_MASK);

    // ---- core 循环：16个NPU核心并行处理 ----
    for (uint32_t core_idx = 0; core_idx < NUM_CORES; ++core_idx) {
        uint32_t group_id = core_idx / 4;
        uint32_t heads_per_core = minicpmv2_head_maping(group_id, layer_id);

        // ---- 生成单核mask ----
        make_single_core_mask(core_idx, npu_mask_single);

        // ---- 构建完整权重、scale、zero_point tensors ----
        const __WeightElem* wt_o = &minicpmv2_weight.group[group_id].layer[layer_id].self_attn.o_proj;
        const __WeightElem* wt_scale = &minicpmv2_weight.group[group_id].layer[layer_id].self_attn.o_proj_scale;
        const __WeightElem* wt_zero = &minicpmv2_weight.group[group_id].layer[layer_id].self_attn.o_proj_zero;

        Tensor weight_full;
        build_tensor(wt_o->addr_dram, OUTPUT_DIM, OUTPUT_DIM, TYPE_INT, WIDTH_4, &weight_full);

        // 计算当前core有多少组scale/zero_point (2个head=1组, 3个head=2组)
        uint32_t num_scale_groups = (heads_per_core == 2) ? 1 : 2;
        Tensor scale_full, zero_full;
        build_tensor(wt_scale->addr_dram, num_scale_groups, OUTPUT_DIM, TYPE_BF, WIDTH_16, &scale_full);
        build_tensor(wt_zero->addr_dram, num_scale_groups, OUTPUT_DIM, TYPE_INT, WIDTH_4, &zero_full);

        // ---- 初始化本core的final累加器 ----
        Tensor core_final_accumulator;
        build_tensor(final_accum_addr, 1, OUTPUT_DIM, TYPE_BF, WIDTH_16, &core_final_accumulator);
        broadcast(zero_bf16, &core_final_accumulator, npu_mask_single);

        // ---- 根据head数量选择处理策略 ----
        if (heads_per_core == 2) {
            // ======== 2个head：直接双head优化 ========

            // 初始化head_accumulator
            build_tensor(head_accum_addr, 1, OUTPUT_DIM, TYPE_BF, WIDTH_16, &head_accumulator);
            broadcast(zero_bf16, &head_accumulator, npu_mask_single);

            // 构建双head输入：从attn_out中取对应的两个head
            uint32_t head0_start_row = 0;
            uint32_t head1_start_row = 1;

            Tensor head0_view, head1_view;
            make_tensor_view(attn_out, head0_start_row, 0, 1, HEAD_DIM, &head0_view); // [1, 64]
            make_tensor_view(attn_out, head1_start_row, 0, 1, HEAD_DIM, &head1_view); // [1, 64]

            // 在intermemory中构建双head输入 [1, 128]
            build_tensor(dual_input_addr, 1, 128, TYPE_BF, WIDTH_16, &input_dual_view);
            Tensor output_view0, output_view1;
            make_tensor_view(&input_dual_view, 0, 0, 1, HEAD_DIM, &output_view0);     // 前64列
            make_tensor_view(&input_dual_view, 0, HEAD_DIM, 1, HEAD_DIM, &output_view1); // 后64列

            load(&head0_view, &output_view0, npu_mask_single);
            load(&head1_view, &output_view1, npu_mask_single);

            // tile_k循环：双head GEMV
            for (uint32_t tile_k = 0; tile_k < NUM_TILE_K; ++tile_k) {
                uint32_t k_start = tile_k * TILE_C;
                uint32_t page_idx = tile_k % 16;
                uint32_t cim_base = CIMC_PAGE_BASE_ADDR + page_idx * CIMC_PAGE_OFFSET;

                // 权重view [128, 128]：前两个head的权重
                uint32_t weight_start_row = 0;
                make_tensor_view(&weight_full, weight_start_row, k_start, 128, TILE_C, &weight_view);
                build_cimc_tensor(cim_base, 128, TILE_C, TYPE_INT, WIDTH_4, CIMC_ROW2COL2, &weight_dst);
                load(&weight_view, &weight_dst, npu_mask_single);

                // 输出到head_accumulator
                Tensor output_view;
                make_tensor_view(&head_accumulator, 0, k_start, 1, TILE_C, &output_view);

                cim_opt.page_index = page_idx;
                cim_opt.accumulate = 0;
                gemm(&input_dual_view, &output_view, &output_view, &cim_opt, npu_mask_single);
            }

            // 反量化：使用scale_index=0 (2个head只有1组)
            Tensor scale_view, zero_view;
            make_tensor_view(&scale_full, 0, 0, 1, OUTPUT_DIM, &scale_view); // [1, 2304]
            make_tensor_view(&zero_full, 0, 0, 1, OUTPUT_DIM, &zero_view);   // [1, 2304]
            apply_dequantization(&head_accumulator, &scale_view, &zero_view, &core_final_accumulator, npu_mask_single);

        } else if (heads_per_core == 3) {
            // ======== 3个head：分两次处理，然后累加 ========

            // 第一次：双head优化
            build_tensor(head_accum_addr, 1, OUTPUT_DIM, TYPE_BF, WIDTH_16, &head_accumulator);
            broadcast(zero_bf16, &head_accumulator, npu_mask_single);

            int pair_type = get_head_pair_type(layer_id, core_idx);
            uint32_t first_head_idx = (pair_type == 0) ? 0 : 1;
            uint32_t second_head_idx = first_head_idx + 1;

            Tensor head0_view, head1_view;
            make_tensor_view(attn_out, first_head_idx, 0, 1, HEAD_DIM, &head0_view);
            make_tensor_view(attn_out, second_head_idx, 0, 1, HEAD_DIM, &head1_view);

            // 构建双head输入
            build_tensor(dual_input_addr, 1, 128, TYPE_BF, WIDTH_16, &input_dual_view);
            Tensor output_view0, output_view1;
            make_tensor_view(&input_dual_view, 0, 0, 1, HEAD_DIM, &output_view0);
            make_tensor_view(&input_dual_view, 0, HEAD_DIM, 1, HEAD_DIM, &output_view1);

            load(&head0_view, &output_view0, npu_mask_single);
            load(&head1_view, &output_view1, npu_mask_single);

            // 双head GEMV
            for (uint32_t tile_k = 0; tile_k < NUM_TILE_K; ++tile_k) {
                uint32_t k_start = tile_k * TILE_C;
                uint32_t page_idx = tile_k % 16;
                uint32_t cim_base = CIMC_PAGE_BASE_ADDR + page_idx * CIMC_PAGE_OFFSET;

                uint32_t weight_start_row = first_head_idx * HEAD_DIM;
                make_tensor_view(&weight_full, weight_start_row, k_start, 128, TILE_C, &weight_view);
                build_cimc_tensor(cim_base, 128, TILE_C, TYPE_INT, WIDTH_4, CIMC_ROW2COL2, &weight_dst);
                load(&weight_view, &weight_dst, npu_mask_single);

                Tensor output_view;
                make_tensor_view(&head_accumulator, 0, k_start, 1, TILE_C, &output_view);

                cim_opt.page_index = page_idx;
                cim_opt.accumulate = 0;
                gemm(&input_dual_view, &output_view, &output_view, &cim_opt, npu_mask_single);
            }

            // 第一次反量化并累加到core_final_accumulator
            int scale_index1 = get_scale_index(layer_id, core_idx, first_head_idx, 1);
            Tensor scale_view1, zero_view1;
            make_tensor_view(&scale_full, scale_index1, 0, 1, OUTPUT_DIM, &scale_view1);
            make_tensor_view(&zero_full, scale_index1, 0, 1, OUTPUT_DIM, &zero_view1);
            apply_dequantization(&head_accumulator, &scale_view1, &zero_view1, &head_accumulator, npu_mask_single);
            add(&core_final_accumulator, &head_accumulator, &core_final_accumulator, &vp_null, npu_mask_single);

            // 第二次：单head处理
            broadcast(zero_bf16, &head_accumulator, npu_mask_single);

            uint32_t single_head_idx = (pair_type == 0) ? 2 : 0;
            make_tensor_view(attn_out, single_head_idx, 0, 1, HEAD_DIM, &input_single_view);

            // 单head GEMV
            for (uint32_t tile_k = 0; tile_k < NUM_TILE_K; ++tile_k) {
                uint32_t k_start = tile_k * TILE_C;
                uint32_t page_idx = tile_k % 16;
                uint32_t cim_base = CIMC_PAGE_BASE_ADDR + page_idx * CIMC_PAGE_OFFSET;

                uint32_t weight_start_row = single_head_idx * HEAD_DIM;
                make_tensor_view(&weight_full, weight_start_row, k_start, HEAD_DIM, TILE_C, &weight_view);
                build_cimc_tensor(cim_base, HEAD_DIM, TILE_C, TYPE_INT, WIDTH_4, CIMC_ROW2COL2, &weight_dst);
                load(&weight_view, &weight_dst, npu_mask_single);

                Tensor output_view;
                make_tensor_view(&head_accumulator, 0, k_start, 1, TILE_C, &output_view);

                cim_opt.page_index = page_idx;
                cim_opt.accumulate = 0;
                gemm(&input_single_view, &output_view, &output_view, &cim_opt, npu_mask_single);
            }

            // 第二次反量化并累加到core_final_accumulator
            int scale_index2 = get_scale_index(layer_id, core_idx, single_head_idx, 0);
            Tensor scale_view2, zero_view2;
            make_tensor_view(&scale_full, scale_index2, 0, 1, OUTPUT_DIM, &scale_view2);
            make_tensor_view(&zero_full, scale_index2, 0, 1, OUTPUT_DIM, &zero_view2);
            apply_dequantization(&head_accumulator, &scale_view2, &zero_view2, &head_accumulator, npu_mask_single);
            add(&core_final_accumulator, &head_accumulator, &core_final_accumulator, &vp_null, npu_mask_single);
        }

        // ---- 累加当前core的结果到最终输出 ----
        add(&final_accumulator, &core_final_accumulator, &final_accumulator, &vp_null, npu_mask_single);

    } // ===== end core_idx =====
}

/// @brief MiniCPMV2 QKV Generation Decode with INT4 Quantization Support
/// @param seq_idx 当前序列位置索引 (从720开始，即decode阶段)
/// @param layer_id 层号, 0~39
/// @param X_spad 输入X(shape=[1,2304]), 单token输入，已储存在spad上  
/// @param q_out Q_proj结果保存到q_out, shape=[heads_total*1, 64]
/// @param k_out K_proj结果保存到k_out, shape=[heads_total*1, 64]  
/// @param v_out V_proj结果保存到v_out, shape=[heads_total*1, 64]
/// @param intermemory 中间计算缓存数组，需要14个块，约8KB (比prefill小)
/// InterMemory布局 (Decode优化版)：
/// [0]: scale参数缓存 [18, 64] BF16 = 2304B
/// [1]: zero参数缓存 [18, 64] INT4 = 576B  
/// [2]: Q量化累加器 [1, 64] BF16 = 128B
/// [3]: K量化累加器 [1, 64] BF16 = 128B
/// [4]: V量化累加器 [1, 64] BF16 = 128B
/// [5]: cos缓存 [1, 64] BF16 = 128B  
/// [6]: sin缓存 [1, 64] BF16 = 128B
/// [7-10]: RoPE Q中间缓存 (4个，每个32B)
/// [11-14]: RoPE K中间缓存 (4个，每个32B)
void minicpmv2_qkvgen_decode_quantized(
    int seq_idx,
    int layer_id, 
    const Tensor* X_spad, 
    const Tensor* q_out,
    const Tensor* k_out,
    const Tensor* v_out,
    InterMemoryArray* intermemory)
{
    // ---- 常量定义 ----
    const uint32_t NUM_CORES = MINICPMV2_NUM_NODES;        // 16
    const uint32_t NUM_GROUPS = MINICPMV2_NUM_GROUPS;      // 4
    const uint32_t HEAD_DIM = MINICPMV2_EMBEDDING_DIM / 36; // 64
    const uint32_t BYTES_PER_ELEM = 2;                      // BF16
    
    // ---- InterMemory验证 ----
    debug_assert(intermemory && intermemory->length >= 15);
    
    // ---- 预分配所有InterMemory地址变量 ----
    uint32_t scale_cache_addr = intermemory->memory[0].base_addr;
    uint32_t zero_cache_addr = intermemory->memory[1].base_addr;
    uint32_t quant_accum_q_addr = intermemory->memory[2].base_addr;
    uint32_t quant_accum_k_addr = intermemory->memory[3].base_addr;
    uint32_t quant_accum_v_addr = intermemory->memory[4].base_addr;
    uint32_t cos_cache_addr = intermemory->memory[5].base_addr;
    uint32_t sin_cache_addr = intermemory->memory[6].base_addr;
    
    // 量化参数和累加器tensor (单token，尺寸为1)
    Tensor scale_tensor, zero_tensor;
    Tensor quant_accumulator_q, quant_accumulator_k, quant_accumulator_v;
    
    // 准备量化参数和累加器tensors (decode：[1, 64]而非[10, 64])
    build_tensor(scale_cache_addr, 18, HEAD_DIM, TYPE_BF, WIDTH_16, &scale_tensor);
    build_tensor(zero_cache_addr, 18, HEAD_DIM, TYPE_INT, WIDTH_4, &zero_tensor);
    build_tensor(quant_accum_q_addr, 1, HEAD_DIM, TYPE_BF, WIDTH_16, &quant_accumulator_q);
    build_tensor(quant_accum_k_addr, 1, HEAD_DIM, TYPE_BF, WIDTH_16, &quant_accumulator_k);
    build_tensor(quant_accum_v_addr, 1, HEAD_DIM, TYPE_BF, WIDTH_16, &quant_accumulator_v);
    
    // ======== 加载 RoPE cos/sin 到缓存 ========
    uint32_t rope_offset = seq_idx * HEAD_DIM * BYTES_PER_ELEM;
    Tensor dst_cos, dst_sin;
    build_tensor(cos_cache_addr, 1, HEAD_DIM, TYPE_BF, WIDTH_16, &dst_cos);
    build_tensor(sin_cache_addr, 1, HEAD_DIM, TYPE_BF, WIDTH_16, &dst_sin);
    
    // 遍历所有group，将各自的cos/sin加载到同一缓存地址
    for (uint32_t g = 0; g < NUM_GROUPS; ++g) {
        int group_mask[4];
        make_group_mask(g, group_mask);
        
        Tensor src_cos, src_sin;
        build_tensor(minicpmv2_weight.group[g].rope.cos.addr_dram + rope_offset,
            1, HEAD_DIM, TYPE_BF, WIDTH_16, &src_cos);
        build_tensor(minicpmv2_weight.group[g].rope.sin.addr_dram + rope_offset,
            1, HEAD_DIM, TYPE_BF, WIDTH_16, &src_sin);
        
        load(&src_cos, &dst_cos, group_mask);
        load(&src_sin, &dst_sin, group_mask);
    }
    
    // ---- Head循环：按head迭代处理 ----
    for (uint32_t head_iter = 0; head_iter < MAX_HEADS_PER_GROUP; ++head_iter) {
        
        // ---- Group循环 ----  
        for (uint32_t group_id = 0; group_id < NUM_GROUPS; ++group_id) {
            uint32_t heads_per_core = minicpmv2_head_maping(group_id, layer_id);
            if (head_iter >= heads_per_core) {
                continue; // 该组无当前head
            }
            
            // 生成group mask
            int npu_mask_group[4];
            make_group_mask(group_id, npu_mask_group);
            
            // ---- 使用输出地址作为psum (避免额外SPAD空间) ----
            uint32_t head_offset_bytes = head_iter * HEAD_DIM * BYTES_PER_ELEM;
            Tensor Q_psum, K_psum, V_psum;
            build_tensor(q_out->base_addr + head_offset_bytes, 1, HEAD_DIM, TYPE_BF, WIDTH_16, &Q_psum);
            build_tensor(k_out->base_addr + head_offset_bytes, 1, HEAD_DIM, TYPE_BF, WIDTH_16, &K_psum);
            build_tensor(v_out->base_addr + head_offset_bytes, 1, HEAD_DIM, TYPE_BF, WIDTH_16, &V_psum);
            
            // ---- 1. 加载当前head的量化参数 ----
            uint32_t scale_head_offset = head_iter * 18 * HEAD_DIM * BYTES_PER_ELEM;
            uint32_t zero_head_offset = head_iter * 18 * HEAD_DIM * BYTES_PER_ELEM;
            
            Tensor scale_src_q, zero_src_q;
            build_tensor(
                minicpmv2_weight.group[group_id].layer[layer_id].self_attn.q_proj_scale.addr_dram + scale_head_offset,
                18, HEAD_DIM, TYPE_BF, WIDTH_16, &scale_src_q);
            build_tensor(
                minicpmv2_weight.group[group_id].layer[layer_id].self_attn.q_proj_zero.addr_dram + zero_head_offset,
                18, HEAD_DIM, TYPE_INT, WIDTH_4, &zero_src_q);
            
            load(&scale_src_q, &scale_tensor, npu_mask_group);
            load(&zero_src_q, &zero_tensor, npu_mask_group);
            
            // ---- 2. 准备VP选项 ----
            VP_Option vp_sub = { .special_case = { 0 }, .operation = OPERATION_SUB, .scalar_in2 = 0 };
            VP_Option vp_mul = { .special_case = { 0 }, .operation = OPERATION_MUL, .scalar_in2 = 0 };
            VP_Option vp_add = { .special_case = { 0 }, .operation = OPERATION_ADD, .scalar_in2 = 0 };
            
            // CIM选项模板
            CIM_Option cim_opt;
            cim_opt.type = TYPE_INT;
            cim_opt.width = WIDTH_4;
            cim_opt.activate = 0;
            cim_opt.accumulate = 0;
            
            // ======== 18个tile_k循环：量化GEMM计算 ========
            for (uint32_t tile_k = 0; tile_k < 18; tile_k++) {
                uint32_t k_start = tile_k * TILE_C;  // 0, 128, 256, ...
                
                // 计算CIM页面索引（Q/K/V错开分配）
                uint32_t page_idx_q = (tile_k * 3 + 0) % 16;
                uint32_t page_idx_k = (tile_k * 3 + 1) % 16;
                uint32_t page_idx_v = (tile_k * 3 + 2) % 16;
                
                uint32_t cim_base_q = CIMC_PAGE_BASE_ADDR + page_idx_q * CIMC_PAGE_OFFSET;
                uint32_t cim_base_k = CIMC_PAGE_BASE_ADDR + page_idx_k * CIMC_PAGE_OFFSET;
                uint32_t cim_base_v = CIMC_PAGE_BASE_ADDR + page_idx_v * CIMC_PAGE_OFFSET;
                
                // 创建X_tile视图 [1, 128] (decode单token)
                Tensor X_tile_view;
                make_tensor_view(X_spad, 0, k_start, 1, TILE_C, &X_tile_view);
                
                // 计算权重地址
                uint32_t wt_stride_row = width_enum_to_bits(WIDTH_4) * HEAD_DIM / 8;  // INT4权重行步长
                uint32_t wt_head_offset = (head_iter * MINICPMV2_EMBEDDING_DIM ) * wt_stride_row;
                uint32_t wt_tile_offset = k_start * wt_stride_row;
                
                // ---- 3.1 Q GEMM + 反量化 + 累加 ----
                const __WeightElem* wt_q = &minicpmv2_weight.group[group_id].layer[layer_id].self_attn.q_proj;
                Tensor wt_src_q, wt_dst_q;
                build_tensor(wt_q->addr_dram + wt_head_offset + wt_tile_offset, 
                            TILE_C, HEAD_DIM, TYPE_INT, WIDTH_4, &wt_src_q);
                build_cimc_tensor(cim_base_q, TILE_C, HEAD_DIM, TYPE_INT, WIDTH_4, CIMC_ROW2COL2, &wt_dst_q);
                
                load(&wt_src_q, &wt_dst_q, npu_mask_group);
                cim_opt.page_index = page_idx_q;
                gemm(&X_tile_view, &quant_accumulator_q, NULL, &cim_opt, npu_mask_group);
                
                // 反量化当前tile结果：用tile_k对应的scale/zero参数
                Tensor scale_tile, zero_tile;
                make_tensor_view(&scale_tensor, tile_k, 0, 1, HEAD_DIM, &scale_tile);  // [1, 64]
                make_tensor_view(&zero_tensor, tile_k, 0, 1, HEAD_DIM, &zero_tile);    // [1, 64]
                
                // 反量化：deq_result = scale * (quant - zero) (decode只有1行)
                // sub(&quant_accumulator_q, &zero_tile, &quant_accumulator_q, &vp_sub, npu_mask_group);
                mul(&quant_accumulator_q, &scale_tile, &quant_accumulator_q, &vp_mul, npu_mask_group);
                
                // 累加到最终Q结果
                if (tile_k == 0) {
                    // 第一次：先清零Q_psum，然后加上deq_result_q
                    sub(&Q_psum, &Q_psum, &Q_psum, &vp_sub, npu_mask_group);  // Q_psum = 0
                }
                add(&Q_psum, &quant_accumulator_q, &Q_psum, &vp_add, npu_mask_group);
                
                // ---- 3.2 K GEMM + 反量化 + 累加 ----
                const __WeightElem* wt_k = &minicpmv2_weight.group[group_id].layer[layer_id].self_attn.k_proj;
                Tensor wt_src_k, wt_dst_k;
                build_tensor(wt_k->addr_dram + wt_head_offset + wt_tile_offset, 
                            TILE_C, HEAD_DIM, TYPE_INT, WIDTH_4, &wt_src_k);
                build_cimc_tensor(cim_base_k, TILE_C, HEAD_DIM, TYPE_INT, WIDTH_4, CIMC_ROW2COL2, &wt_dst_k);
                
                load(&wt_src_k, &wt_dst_k, npu_mask_group);
                cim_opt.page_index = page_idx_k;
                gemm(&X_tile_view, &quant_accumulator_k, &quant_accumulator_k, &cim_opt, npu_mask_group);
                
                // K反量化+累加 
                // sub(&quant_accumulator_k, &zero_tile, &quant_accumulator_k, &vp_sub, npu_mask_group);
                mul(&quant_accumulator_k, &scale_tile, &quant_accumulator_k, &vp_mul, npu_mask_group);
                
                // K累加到最终结果
                if (tile_k == 0) {
                    sub(&K_psum, &K_psum, &K_psum, &vp_sub, npu_mask_group);  // K_psum = 0
                }
                add(&K_psum, &quant_accumulator_k, &K_psum, &vp_add, npu_mask_group);
                
                // ---- 3.3 V GEMM + 反量化 + 累加 ----
                const __WeightElem* wt_v = &minicpmv2_weight.group[group_id].layer[layer_id].self_attn.v_proj;
                Tensor wt_src_v, wt_dst_v;
                build_tensor(wt_v->addr_dram + wt_head_offset + wt_tile_offset, 
                            TILE_C, HEAD_DIM, TYPE_INT, WIDTH_4, &wt_src_v);
                build_cimc_tensor(cim_base_v, TILE_C, HEAD_DIM, TYPE_INT, WIDTH_4, CIMC_ROW2COL2, &wt_dst_v);
                
                load(&wt_src_v, &wt_dst_v, npu_mask_group);
                cim_opt.page_index = page_idx_v;
                gemm(&X_tile_view, &quant_accumulator_v, &quant_accumulator_v, &cim_opt, npu_mask_group);
                
                // V反量化+累加
                // sub(&quant_accumulator_v, &zero_tile, &quant_accumulator_v, &vp_sub, npu_mask_group);
                mul(&quant_accumulator_v, &scale_tile, &quant_accumulator_v, &vp_mul, npu_mask_group);
                
                // V累加到最终结果
                if (tile_k == 0) {
                    sub(&V_psum, &V_psum, &V_psum, &vp_sub, npu_mask_group);  // V_psum = 0
                }
                add(&V_psum, &quant_accumulator_v, &V_psum, &vp_add, npu_mask_group);
            }
        }
        
        // ======== 所有Group的GEMM完毕，进行RoPE处理 ========
        VP_Option vp_null = { .special_case = { 0 }, .operation = OPERATION_NULL, .scalar_in2 = 0 };
        int full_mask[4] = { 0xf, 0xf, 0xf, 0xf }; // 使用全部16核进行RoPE
        
        // RoPE InterMemory
        InterMemoryArray rope_mem_q = { .memory = &intermemory->memory[7], .length = 4 };
        InterMemoryArray rope_mem_k = { .memory = &intermemory->memory[11], .length = 4 };
        
        // 对Q和K进行RoPE，对完整的psum执行，使用full_mask
        uint32_t head_offset_bytes = head_iter * HEAD_DIM * BYTES_PER_ELEM;
        Tensor Q_psum_all, K_psum_all;
        build_tensor(q_out->base_addr + head_offset_bytes, 1, HEAD_DIM, TYPE_BF, WIDTH_16, &Q_psum_all);
        build_tensor(k_out->base_addr + head_offset_bytes, 1, HEAD_DIM, TYPE_BF, WIDTH_16, &K_psum_all);
        
        rope(&Q_psum_all, &Q_psum_all, &dst_sin, &dst_cos, &rope_mem_q, &vp_null, full_mask);
        rope(&K_psum_all, &K_psum_all, &dst_sin, &dst_cos, &rope_mem_k, &vp_null, full_mask);
        
        // V不需要RoPE，GEMM结果已直接存储在输出地址
    }
}
